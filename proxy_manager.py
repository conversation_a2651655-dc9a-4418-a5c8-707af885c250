"""
Quả<PERSON> lý proxy miễ<PERSON> phí cho tool đăng ký tài kho<PERSON>n
"""

import requests
import threading
import time
import random
from concurrent.futures import ThreadPoolExecutor, as_completed
from config import PROXY_CONFIG, OUTPUT_CONFIG
import logging

class ProxyManager:
    def __init__(self):
        self.working_proxies = []
        self.used_proxies = set()
        self.proxy_lock = threading.Lock()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(OUTPUT_CONFIG['log_file']),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def fetch_proxies_from_source(self, url):
        """Lấy proxy từ một nguồn"""
        try:
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                proxies = []
                lines = response.text.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if ':' in line and len(line.split(':')) == 2:
                        ip, port = line.split(':')
                        if self._is_valid_ip(ip) and port.isdigit():
                            proxies.append(f"{ip}:{port}")
                return proxies
        except Exception as e:
            self.logger.error(f"Lỗi khi lấy proxy từ {url}: {e}")
        return []

    def _is_valid_ip(self, ip):
        """Kiểm tra IP hợp lệ"""
        try:
            parts = ip.split('.')
            return len(parts) == 4 and all(0 <= int(part) <= 255 for part in parts)
        except:
            return False

    def test_proxy(self, proxy):
        """Test một proxy"""
        try:
            proxy_dict = {
                'http': f'http://{proxy}',
                'https': f'http://{proxy}'
            }
            
            response = requests.get(
                'http://httpbin.org/ip',
                proxies=proxy_dict,
                timeout=PROXY_CONFIG['proxy_timeout']
            )
            
            if response.status_code == 200:
                return proxy
        except:
            pass
        return None

    def fetch_and_test_proxies(self):
        """Lấy và test tất cả proxy"""
        self.logger.info("Bắt đầu lấy danh sách proxy...")
        
        all_proxies = []
        for source in PROXY_CONFIG['proxy_sources']:
            proxies = self.fetch_proxies_from_source(source)
            all_proxies.extend(proxies)
            self.logger.info(f"Lấy được {len(proxies)} proxy từ {source}")
        
        # Loại bỏ duplicate
        all_proxies = list(set(all_proxies))
        self.logger.info(f"Tổng cộng {len(all_proxies)} proxy unique")
        
        # Test proxy đồng thời
        self.logger.info("Bắt đầu test proxy...")
        working_proxies = []
        
        with ThreadPoolExecutor(max_workers=PROXY_CONFIG['max_proxy_test_threads']) as executor:
            future_to_proxy = {executor.submit(self.test_proxy, proxy): proxy for proxy in all_proxies}
            
            for future in as_completed(future_to_proxy):
                result = future.result()
                if result:
                    working_proxies.append(result)
                    self.logger.info(f"Proxy hoạt động: {result}")
        
        self.working_proxies = working_proxies
        self.logger.info(f"Tìm được {len(working_proxies)} proxy hoạt động")
        
        # Lưu proxy hoạt động
        self.save_working_proxies()
        
        return len(working_proxies)

    def get_proxy(self):
        """Lấy một proxy chưa sử dụng"""
        with self.proxy_lock:
            available_proxies = [p for p in self.working_proxies if p not in self.used_proxies]
            
            if not available_proxies:
                self.logger.warning("Hết proxy khả dụng!")
                return None
            
            proxy = random.choice(available_proxies)
            self.used_proxies.add(proxy)
            return proxy

    def release_proxy(self, proxy):
        """Trả lại proxy để sử dụng lại"""
        with self.proxy_lock:
            if proxy in self.used_proxies:
                self.used_proxies.remove(proxy)

    def save_working_proxies(self):
        """Lưu danh sách proxy hoạt động"""
        try:
            with open(OUTPUT_CONFIG['proxy_file'], 'w') as f:
                for proxy in self.working_proxies:
                    f.write(f"{proxy}\n")
            self.logger.info(f"Đã lưu {len(self.working_proxies)} proxy vào {OUTPUT_CONFIG['proxy_file']}")
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu proxy: {e}")

    def load_working_proxies(self):
        """Load proxy đã lưu"""
        try:
            with open(OUTPUT_CONFIG['proxy_file'], 'r') as f:
                self.working_proxies = [line.strip() for line in f if line.strip()]
            self.logger.info(f"Đã load {len(self.working_proxies)} proxy từ file")
            return len(self.working_proxies)
        except FileNotFoundError:
            self.logger.info("Không tìm thấy file proxy đã lưu")
            return 0
        except Exception as e:
            self.logger.error(f"Lỗi khi load proxy: {e}")
            return 0
