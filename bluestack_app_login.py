"""
Tool tự động đăng nhập app 13win trên BlueStacks để nhận thưởng "Tải xuống, đăng nhập"
"""

import time
import subprocess
import pyautogui
import cv2
import numpy as np
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

# Cấu hình pyautogui
pyautogui.FAILSAFE = True
pyautogui.PAUSE = 1

class BlueStacksController:
    def __init__(self):
        self.bluestacks_path = r"C:\Program Files\BlueStacks_nxt\HD-Player.exe"
        self.app_package = "com.win13.app"  # Package name của 13win app
        
    def start_bluestacks(self):
        """Khởi động BlueStacks"""
        try:
            print("🚀 Khởi động BlueStacks...")
            subprocess.Popen([self.bluestacks_path])
            time.sleep(15)  # Chờ BlueStacks khởi động
            print("✅ BlueStacks đã khởi động")
            return True
        except Exception as e:
            print(f"❌ Lỗi khởi động BlueStacks: {e}")
            return False
    
    def find_and_click_image(self, image_path, confidence=0.8, timeout=10):
        """Tìm và click vào hình ảnh trên màn hình"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                location = pyautogui.locateOnScreen(image_path, confidence=confidence)
                if location:
                    center = pyautogui.center(location)
                    pyautogui.click(center)
                    print(f"✅ Đã click vào {image_path}")
                    return True
            except:
                pass
            time.sleep(1)
        
        print(f"❌ Không tìm thấy {image_path}")
        return False
    
    def open_13win_app(self):
        """Mở app 13win trên BlueStacks"""
        try:
            print("📱 Mở app 13win...")
            
            # Tìm và click icon app 13win
            # Bạn cần chụp screenshot icon app và lưu thành file
            if self.find_and_click_image("images/13win_icon.png", timeout=15):
                time.sleep(5)  # Chờ app khởi động
                print("✅ App 13win đã mở")
                return True
            else:
                print("❌ Không tìm thấy icon app 13win")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi mở app: {e}")
            return False
    
    def login_app(self, username, password):
        """Đăng nhập vào app 13win"""
        try:
            print(f"🔑 Đăng nhập app với tài khoản: {username}")
            
            # Chờ trang đăng nhập xuất hiện
            time.sleep(3)
            
            # Click vào ô username (cần chụp screenshot và lưu)
            if self.find_and_click_image("images/username_field.png"):
                time.sleep(1)
                pyautogui.typewrite(username)
                print(f"✅ Đã nhập username: {username}")
            
            # Click vào ô password
            if self.find_and_click_image("images/password_field.png"):
                time.sleep(1)
                pyautogui.typewrite(password)
                print(f"✅ Đã nhập password")
            
            # Click nút đăng nhập
            if self.find_and_click_image("images/login_button.png"):
                time.sleep(5)  # Chờ đăng nhập
                print("✅ Đã click nút đăng nhập")
                
                # Kiểm tra đăng nhập thành công
                if self.find_and_click_image("images/home_screen.png", timeout=10):
                    print(f"{Fore.GREEN}🎉 ĐĂNG NHẬP APP THÀNH CÔNG!{Style.RESET_ALL}")
                    return True
                else:
                    print(f"{Fore.RED}❌ Đăng nhập app thất bại{Style.RESET_ALL}")
                    return False
            
        except Exception as e:
            print(f"❌ Lỗi đăng nhập app: {e}")
            return False
    
    def close_app(self):
        """Đóng app và BlueStacks"""
        try:
            print("🔄 Đóng app...")
            # Alt + F4 để đóng
            pyautogui.hotkey('alt', 'f4')
            time.sleep(2)
            print("✅ Đã đóng app")
        except Exception as e:
            print(f"⚠️  Lỗi đóng app: {e}")

def setup_image_templates():
    """Tạo thư mục và hướng dẫn chụp screenshot"""
    import os
    
    if not os.path.exists("images"):
        os.makedirs("images")
        print("📁 Đã tạo thư mục images/")
    
    print(f"\n{Fore.YELLOW}📸 HƯỚNG DẪN CHỤP SCREENSHOT:{Style.RESET_ALL}")
    print("Bạn cần chụp các screenshot sau và lưu vào thư mục images/:")
    print("1. 13win_icon.png - Icon app 13win trên BlueStacks")
    print("2. username_field.png - Ô nhập username trong app")
    print("3. password_field.png - Ô nhập password trong app") 
    print("4. login_button.png - Nút đăng nhập trong app")
    print("5. home_screen.png - Màn hình chính sau khi đăng nhập")
    print("\n💡 Mẹo: Chụp screenshot nhỏ, chỉ phần cần thiết")

def claim_web_rewards(driver, user_id="391111507"):
    """Nhận thưởng trên web sau khi đăng nhập app"""
    try:
        print(f"\n🌐 Nhận thưởng trên web...")
        
        # URL trang nhiệm vụ
        task_url = f"https://www.13win16.com/home/<USER>"
        driver.get(task_url)
        time.sleep(3)
        
        print(f"📄 URL: {task_url}")
        
        # Tìm nút "Nhận" cho nhiệm vụ "Tải xuống, đăng nhập"
        claim_buttons = []
        
        # Tìm các nút "Nhận"
        try:
            buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Nhận')]")
            claim_buttons.extend(buttons)
            print(f"✅ Tìm thấy {len(buttons)} nút 'Nhận'")
        except:
            pass
        
        if not claim_buttons:
            print("⚠️  Không tìm thấy nút 'Nhận'")
            return False
        
        # Click từng nút "Nhận"
        claimed_count = 0
        
        for i, button in enumerate(claim_buttons):
            try:
                if button.is_enabled() and button.is_displayed():
                    # Scroll đến nút
                    driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(1)
                    
                    # Click nút
                    button.click()
                    claimed_count += 1
                    print(f"✅ Đã nhận thưởng {i+1}")
                    time.sleep(2)
                    
            except Exception as e:
                print(f"⚠️  Lỗi click nút {i+1}: {e}")
        
        print(f"🎁 Đã nhận {claimed_count} phần thưởng trên web")
        return claimed_count > 0
        
    except Exception as e:
        print(f"❌ Lỗi nhận thưởng web: {e}")
        return False

def complete_download_login_task(username, password):
    """Hoàn thành nhiệm vụ 'Tải xuống, đăng nhập' """
    print(f"\n🎯 HOÀN THÀNH NHIỆM VỤ: Tải xuống, đăng nhập")
    print("="*60)
    print(f"👤 Tài khoản: {username}")
    print(f"🔒 Password: {password}")
    
    # BƯỚC 1: Đăng nhập app trên BlueStacks
    bluestacks = BlueStacksController()
    
    print(f"\n📱 BƯỚC 1: Đăng nhập app trên BlueStacks")
    
    if not bluestacks.start_bluestacks():
        return False
    
    if not bluestacks.open_13win_app():
        return False
    
    if not bluestacks.login_app(username, password):
        return False
    
    # Chờ một chút để hệ thống ghi nhận
    print("⏳ Chờ hệ thống ghi nhận đăng nhập app...")
    time.sleep(10)
    
    # Đóng app
    bluestacks.close_app()
    
    # BƯỚC 2: Nhận thưởng trên web
    print(f"\n🌐 BƯỚC 2: Nhận thưởng trên web")
    
    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')
        
        # Sử dụng ChromeDriver local
        chromedriver_path = "drivers/chromedriver.exe"
        service = Service(chromedriver_path)
        
        # Tạo driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(5)
        
        # Đăng nhập web
        print("🔑 Đăng nhập web...")
        login_url = "https://www.13win16.com/home/<USER>"
        driver.get(login_url)
        time.sleep(3)
        
        # Điền form đăng nhập web
        username_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder*="điện thoại"], input[type="text"]'))
        )
        username_field.clear()
        username_field.send_keys(username)
        
        password_field = driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
        password_field.clear()
        password_field.send_keys(password)
        
        login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"], button')
        login_button.click()
        
        time.sleep(5)
        
        # Nhận thưởng
        if claim_web_rewards(driver):
            print(f"{Fore.GREEN}🎉 HOÀN THÀNH! Đã nhận thưởng 'Tải xuống, đăng nhập'{Style.RESET_ALL}")
            return True
        else:
            print(f"{Fore.YELLOW}⚠️  Không thể nhận thưởng (có thể chưa cập nhật){Style.RESET_ALL}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
        
    finally:
        if driver:
            try:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                driver.quit()
            except:
                pass

def main():
    """Main function"""
    try:
        print("📱 TOOL HOÀN THÀNH NHIỆM VỤ 'TẢI XUỐNG, ĐĂNG NHẬP'")
        print("="*60)
        print("Tool này sẽ:")
        print("1. Khởi động BlueStacks")
        print("2. Mở app 13win")
        print("3. Đăng nhập vào app")
        print("4. Quay lại web để nhận thưởng 35.00 D")
        
        # Kiểm tra setup
        setup_image_templates()
        
        # Nhập thông tin tài khoản
        print(f"\n📝 Nhập thông tin tài khoản:")
        username = input("👤 Username: ").strip()
        password = input("🔒 Password: ").strip()
        
        if not username or not password:
            print("❌ Vui lòng nhập đầy đủ thông tin!")
            return
        
        confirm = input(f"\n{Fore.YELLOW}Tiếp tục với tài khoản {username}? (y/n): {Style.RESET_ALL}").lower()
        
        if confirm == 'y':
            success = complete_download_login_task(username, password)
            
            if success:
                print(f"\n{Fore.GREEN}🎉 HOÀN THÀNH THÀNH CÔNG!{Style.RESET_ALL}")
                print("✅ Đã đăng nhập app")
                print("🎁 Đã nhận thưởng 35.00 D")
            else:
                print(f"\n{Fore.RED}❌ THẤT BẠI!{Style.RESET_ALL}")
        else:
            print("❌ Đã hủy!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
