"""
Tạ<PERSON> thông tin tài khoản ngẫu nhiên
"""

import random
import string
import os
import time
from faker import Faker
from config import ACCOUNT_CONFIG
import logging

class AccountGenerator:
    def __init__(self):
        self.fake = Faker(['vi_VN', 'en_US'])  # Hỗ trợ tiếng Việt và tiếng Anh
        self.logger = logging.getLogger(__name__)
        self.username_counter = self.load_username_counter()

    def load_username_counter(self):
        """Load số thứ tự username từ file"""
        try:
            if os.path.exists(ACCOUNT_CONFIG['username_counter_file']):
                with open(ACCOUNT_CONFIG['username_counter_file'], 'r', encoding='utf-8') as f:
                    return int(f.read().strip())
            return 1
        except:
            return 1

    def save_username_counter(self):
        """Lưu số thứ tự username vào file"""
        try:
            with open(ACCOUNT_CONFIG['username_counter_file'], 'w', encoding='utf-8') as f:
                f.write(str(self.username_counter))
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu username counter: {e}")

    def generate_sequential_username(self):
        """Tạo username theo thứ tự taolatrumnohu1, taolatrumnohu2, ..."""
        username = f"{ACCOUNT_CONFIG['username_prefix']}{self.username_counter}"
        self.username_counter += 1
        self.save_username_counter()
        return username

    def generate_random_username(self):
        """Tạo username ngẫu nhiên khi tài khoản đã tồn tại"""
        # Kết hợp prefix với số ngẫu nhiên
        random_suffix = random.randint(10000, 99999)
        timestamp = str(int(time.time()))[-4:]  # 4 số cuối của timestamp
        return f"{ACCOUNT_CONFIG['username_prefix']}{random_suffix}{timestamp}"

    def generate_username(self, length=8):
        """Tạo username ngẫu nhiên"""
        # Kết hợp tên và số
        first_name = self.fake.first_name().lower()
        numbers = ''.join(random.choices(string.digits, k=4))
        username = f"{first_name}{numbers}"

        # Đảm bảo độ dài phù hợp
        if len(username) > length:
            username = username[:length]
        elif len(username) < length:
            username += ''.join(random.choices(string.ascii_lowercase + string.digits,
                                             k=length - len(username)))

        return username

    def generate_email(self):
        """Tạo email ngẫu nhiên"""
        username = self.generate_username(10)
        domain = random.choice(ACCOUNT_CONFIG['email_domains'])
        return f"{username}@{domain}"

    def generate_password(self):
        """Tạo mật khẩu mạnh"""
        length = ACCOUNT_CONFIG['password_length']

        # Đảm bảo có ít nhất 1 ký tự từ mỗi loại
        password = [
            random.choice(string.ascii_uppercase),  # Chữ hoa
            random.choice(string.ascii_lowercase),  # Chữ thường
            random.choice(string.digits),           # Số
            random.choice('!@#$%^&*')              # Ký tự đặc biệt
        ]

        # Thêm các ký tự ngẫu nhiên
        all_chars = string.ascii_letters + string.digits + '!@#$%^&*'
        for _ in range(length - 4):
            password.append(random.choice(all_chars))

        # Trộn ngẫu nhiên
        random.shuffle(password)
        return ''.join(password)

    def generate_phone(self):
        """Tạo số điện thoại ngẫu nhiên"""
        country_code = random.choice(ACCOUNT_CONFIG['phone_country_codes'])

        if country_code == "+84":  # Việt Nam
            # Số điện thoại Việt Nam: +84 + 9 số
            phone_number = ''.join(random.choices(string.digits, k=9))
            # Đảm bảo số đầu hợp lệ (3, 5, 7, 8, 9)
            phone_number = random.choice(['3', '5', '7', '8', '9']) + phone_number[1:]
        else:
            # Số điện thoại quốc tế: 10 số
            phone_number = ''.join(random.choices(string.digits, k=10))

        return f"{country_code}{phone_number}"

    def generate_name(self):
        """Tạo họ tên ngẫu nhiên"""
        return {
            'first_name': self.fake.first_name(),
            'last_name': self.fake.last_name(),
            'full_name': self.fake.name()
        }

    def generate_birth_date(self):
        """Tạo ngày sinh ngẫu nhiên"""
        age = random.randint(ACCOUNT_CONFIG['min_age'], ACCOUNT_CONFIG['max_age'])
        birth_date = self.fake.date_of_birth(minimum_age=age, maximum_age=age)
        return {
            'birth_date': birth_date,
            'day': birth_date.day,
            'month': birth_date.month,
            'year': birth_date.year
        }

    def generate_address(self):
        """Tạo địa chỉ ngẫu nhiên"""
        return {
            'address': self.fake.address(),
            'city': self.fake.city(),
            'country': self.fake.country(),
            'postal_code': self.fake.postcode()
        }

    def generate_complete_account(self, use_sequential_username=True):
        """Tạo thông tin tài khoản đầy đủ"""
        birth_info = self.generate_birth_date()
        address_info = self.generate_address()

        # Sử dụng username tuần tự hoặc ngẫu nhiên
        if use_sequential_username:
            username = self.generate_sequential_username()
        else:
            username = self.generate_random_username()

        account = {
            'username': username,
            'email': self.generate_email(),
            'password': self.generate_password(),
            'phone': self.generate_phone(),
            'first_name': ACCOUNT_CONFIG['fixed_real_name'].split()[0],  # Họ
            'last_name': ' '.join(ACCOUNT_CONFIG['fixed_real_name'].split()[1:]),  # Tên
            'full_name': ACCOUNT_CONFIG['fixed_real_name'],  # Họ tên đầy đủ cố định
            'birth_day': birth_info['day'],
            'birth_month': birth_info['month'],
            'birth_year': birth_info['year'],
            'address': address_info['address'],
            'city': address_info['city'],
            'country': address_info['country'],
            'postal_code': address_info['postal_code']
        }

        self.logger.info(f"Đã tạo tài khoản: {account['username']} - {account['full_name']}")
        return account

    def generate_account_for_retry(self):
        """Tạo tài khoản với username ngẫu nhiên khi tài khoản đã tồn tại"""
        return self.generate_complete_account(use_sequential_username=False)

    def save_account(self, account, filename):
        """Lưu thông tin tài khoản vào file"""
        try:
            # Tạo thư mục output nếu chưa có
            import os
            os.makedirs('output', exist_ok=True)

            filepath = os.path.join('output', filename)

            with open(filepath, 'a', encoding='utf-8') as f:
                # Thêm timestamp
                import datetime
                timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                account_line = (
                    f"[{timestamp}] "
                    f"Username: {account['username']} | "
                    f"Password: {account['password']} | "
                    f"Real Name: {account['full_name']} | "
                    f"Email: {account.get('email', 'N/A')} | "
                    f"Phone: {account.get('phone', 'N/A')}\n"
                )
                f.write(account_line)

            self.logger.info(f"Đã lưu tài khoản vào {filepath}")
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu tài khoản: {e}")

# Test function
if __name__ == "__main__":
    generator = AccountGenerator()
    account = generator.generate_complete_account()
    print("Thông tin tài khoản mẫu:")
    for key, value in account.items():
        print(f"{key}: {value}")
