"""
Tool debug để kiểm tra form đăng ký và tìm selector chính xác
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def debug_registration_form():
    """Debug form đăng ký để tìm selector chính xác"""
    print("🔍 DEBUG FORM ĐĂNG KÝ 13WIN")
    print("="*50)
    
    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')
        
        # Sử dụng ChromeDriver local
        chromedriver_path = "drivers/chromedriver.exe"
        service = Service(chromedriver_path)
        
        # Tạo driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(5)
        
        # Điều hướng đến trang đăng ký
        register_url = "https://www.13win16.com/home/<USER>"
        print(f"🌐 Đi đến: {register_url}")
        driver.get(register_url)
        time.sleep(5)
        
        print("\n📋 PHÂN TÍCH FORM:")
        
        # 1. Tìm tất cả input
        print("\n1️⃣ TẤT CẢ INPUT:")
        inputs = driver.find_elements(By.CSS_SELECTOR, 'input')
        for i, inp in enumerate(inputs):
            try:
                input_type = inp.get_attribute('type') or 'text'
                placeholder = inp.get_attribute('placeholder') or 'Không có'
                name = inp.get_attribute('name') or 'Không có'
                id_attr = inp.get_attribute('id') or 'Không có'
                
                print(f"  Input {i+1}: type='{input_type}', placeholder='{placeholder}', name='{name}', id='{id_attr}'")
            except:
                print(f"  Input {i+1}: Lỗi đọc thuộc tính")
        
        # 2. Tìm tất cả button
        print("\n2️⃣ TẤT CẢ BUTTON:")
        buttons = driver.find_elements(By.CSS_SELECTOR, 'button')
        for i, btn in enumerate(buttons):
            try:
                button_type = btn.get_attribute('type') or 'button'
                text = btn.text or 'Không có text'
                class_attr = btn.get_attribute('class') or 'Không có'
                
                print(f"  Button {i+1}: type='{button_type}', text='{text}', class='{class_attr}'")
            except:
                print(f"  Button {i+1}: Lỗi đọc thuộc tính")
        
        # 3. Tìm form
        print("\n3️⃣ FORM:")
        forms = driver.find_elements(By.CSS_SELECTOR, 'form')
        for i, form in enumerate(forms):
            try:
                action = form.get_attribute('action') or 'Không có'
                method = form.get_attribute('method') or 'Không có'
                
                print(f"  Form {i+1}: action='{action}', method='{method}'")
            except:
                print(f"  Form {i+1}: Lỗi đọc thuộc tính")
        
        # 4. Chụp screenshot
        try:
            driver.save_screenshot("debug_registration_form.png")
            print(f"\n📸 Đã chụp screenshot: debug_registration_form.png")
        except:
            pass
        
        # 5. Lưu HTML source
        try:
            with open("debug_page_source.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print(f"📄 Đã lưu HTML source: debug_page_source.html")
        except:
            pass
        
        print(f"\n{Fore.GREEN}✅ DEBUG HOÀN THÀNH!{Style.RESET_ALL}")
        print("📋 Kiểm tra:")
        print("  - debug_registration_form.png (screenshot)")
        print("  - debug_page_source.html (HTML source)")
        
        # Test điền form
        print(f"\n{Fore.YELLOW}🧪 TEST ĐIỀN FORM:{Style.RESET_ALL}")
        
        test_choice = input("Có muốn test điền form không? (y/n): ").lower()
        
        if test_choice == 'y':
            test_fill_form(driver)
        
    except Exception as e:
        print(f"❌ Lỗi debug: {e}")
        
    finally:
        if driver:
            try:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                driver.quit()
            except:
                pass

def test_fill_form(driver):
    """Test điền form với dữ liệu thật"""
    try:
        print("🧪 Bắt đầu test điền form...")
        
        # Test data
        username = "testuser123"
        password = "testpass123"
        full_name = "TRAN HOANG AN"
        
        # 1. Username
        print("\n1️⃣ Điền Username:")
        text_inputs = driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
        if not text_inputs:
            text_inputs = driver.find_elements(By.CSS_SELECTOR, 'input')
        
        if text_inputs:
            element = text_inputs[0]
            element.clear()
            element.send_keys(username)
            print(f"✅ Đã điền username: {username}")
            time.sleep(1)
        else:
            print("❌ Không tìm thấy ô username")
        
        # 2. Password
        print("\n2️⃣ Điền Password:")
        password_inputs = driver.find_elements(By.CSS_SELECTOR, 'input[type="password"]')
        if password_inputs:
            element = password_inputs[0]
            element.clear()
            element.send_keys(password)
            print(f"✅ Đã điền password: {password}")
            time.sleep(1)
        else:
            print("❌ Không tìm thấy ô password")
        
        # 3. Confirm Password
        print("\n3️⃣ Điền Confirm Password:")
        if len(password_inputs) >= 2:
            element = password_inputs[1]
            element.clear()
            element.send_keys(password)
            print("✅ Đã điền confirm password")
            time.sleep(1)
        else:
            print("⚠️  Không tìm thấy ô confirm password")
        
        # 4. Họ tên
        print("\n4️⃣ Điền Họ tên:")
        name_input = None
        try:
            name_input = driver.find_element(By.XPATH, "//input[contains(@placeholder, 'Họ') or contains(@placeholder, 'tên') or contains(@placeholder, 'Name')]")
        except:
            text_inputs = driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
            if len(text_inputs) >= 2:
                name_input = text_inputs[1]
        
        if name_input:
            name_input.clear()
            name_input.send_keys(full_name)
            print(f"✅ Đã điền họ tên: {full_name}")
            time.sleep(1)
        else:
            print("❌ Không tìm thấy ô họ tên")
        
        # 5. Checkbox
        print("\n5️⃣ Checkbox:")
        try:
            checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
            if not checkbox.is_selected():
                checkbox.click()
                print("✅ Đã tick checkbox")
            else:
                print("✅ Checkbox đã được tick")
        except:
            print("⚠️  Không tìm thấy checkbox")
        
        # 6. Submit button
        print("\n6️⃣ Tìm nút Submit:")
        submit_button = None
        
        try:
            submit_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"]')
            print("✅ Tìm thấy button[type='submit']")
        except:
            try:
                submit_button = driver.find_element(By.XPATH, "//button[contains(text(), 'Đăng ký')]")
                print("✅ Tìm thấy button có text 'Đăng ký'")
            except:
                buttons = driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    submit_button = buttons[-1]
                    print("✅ Sử dụng button cuối cùng")
        
        if submit_button:
            print(f"📋 Button text: '{submit_button.text}'")
            
            submit_choice = input(f"\n{Fore.YELLOW}Có muốn click submit không? (y/n): {Style.RESET_ALL}").lower()
            
            if submit_choice == 'y':
                submit_button.click()
                print("🚀 Đã click submit!")
                time.sleep(5)
                
                # Kiểm tra kết quả
                page_source = driver.page_source.lower()
                if any(keyword in page_source for keyword in ["thành công", "success", "welcome"]):
                    print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
                elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already"]):
                    print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                else:
                    print(f"{Fore.RED}❓ Không xác định được kết quả{Style.RESET_ALL}")
                    
                # Chụp screenshot kết quả
                try:
                    driver.save_screenshot("test_result.png")
                    print("📸 Đã chụp screenshot kết quả: test_result.png")
                except:
                    pass
        else:
            print("❌ Không tìm thấy nút submit")
        
    except Exception as e:
        print(f"❌ Lỗi test form: {e}")

def main():
    """Main function"""
    try:
        debug_registration_form()
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
