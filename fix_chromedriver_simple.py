"""
Script đơn giản sửa lỗi ChromeDriver
"""

import os
import shutil
from pathlib import Path
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def clean_chromedriver_cache():
    """Xóa cache ChromeDriver"""
    print(f"{Fore.YELLOW}🧹 Dọn dẹp cache ChromeDriver...{Style.RESET_ALL}")
    
    # Thư mục cache WebDriver Manager
    cache_dirs = [
        os.path.expanduser("~/.wdm"),
        os.path.expanduser("~/AppData/Local/.wdm"),
        os.path.expanduser("~/AppData/Roaming/.wdm"),
        ".wdm",
        "drivers"
    ]
    
    cleaned = 0
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"✅ Đã xóa: {cache_dir}")
                cleaned += 1
            except Exception as e:
                print(f"⚠️  Không thể xóa {cache_dir}: {e}")
    
    if cleaned == 0:
        print("ℹ️  Không tìm thấy cache ChromeDriver")
    else:
        print(f"✅ Đã xóa {cleaned} thư mục cache")
    
    return True

def test_chromedriver():
    """Test ChromeDriver đơn giản"""
    print(f"{Fore.BLUE}🧪 Test ChromeDriver...{Style.RESET_ALL}")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.chrome.service import Service
        
        # Cấu hình Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--disable-extensions')
        
        print("📥 Đang tải ChromeDriver...")
        
        # Tạo service và driver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("🌐 Test truy cập Google...")
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ ChromeDriver hoạt động tốt! (Test page: {title})")
        return True
        
    except Exception as e:
        print(f"❌ ChromeDriver không hoạt động: {e}")
        return False

def main():
    """Main function"""
    print("🔧 SỬA LỖI CHROMEDRIVER - PHIÊN BẢN ĐỠN GIẢN")
    print("="*60)
    
    print("Script này sẽ:")
    print("1. Xóa cache ChromeDriver cũ")
    print("2. Tải ChromeDriver mới")
    print("3. Test ChromeDriver")
    
    confirm = input(f"\n{Fore.GREEN}Tiếp tục? (y/n): {Style.RESET_ALL}").lower()
    if confirm != 'y':
        print("❌ Đã hủy!")
        return
    
    try:
        # Bước 1: Xóa cache
        clean_chromedriver_cache()
        
        # Bước 2: Test ChromeDriver
        print(f"\n{Fore.BLUE}🧪 Test ChromeDriver mới...{Style.RESET_ALL}")
        if test_chromedriver():
            print(f"\n{Fore.GREEN}🎉 ĐÃ SỬA XONG LỖI CHROMEDRIVER!{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 Bây giờ có thể chạy tool bình thường{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.RED}❌ VẪN CÒN LỖI CHROMEDRIVER{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 Thử các giải pháp khác:{Style.RESET_ALL}")
            print("1. Cài đặt lại Google Chrome")
            print("2. Chạy với quyền Administrator")
            print("3. Tắt antivirus tạm thời")
            print("4. Kiểm tra Windows Defender")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
