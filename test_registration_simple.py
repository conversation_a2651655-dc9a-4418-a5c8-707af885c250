"""
Test đăng ký đơn giản với selector cố định
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def test_registration():
    """Test đăng ký với selector cố định"""
    print("🧪 TEST ĐĂNG KÝ ĐƠN GIẢN")
    print("="*50)
    
    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')
        
        # Sử dụng ChromeDriver local
        chromedriver_path = "drivers/chromedriver.exe"
        if os.path.exists(chromedriver_path):
            print(f"✅ Sử dụng ChromeDriver local: {chromedriver_path}")
            service = Service(chromedriver_path)
        else:
            print("❌ Không tìm thấy ChromeDriver local")
            return False
        
        # Tạo driver
        print("🚀 Đang tạo browser...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(5)  # Giảm timeout
        
        print("✅ Browser đã tạo thành công")
        
        # Điều hướng đến trang đăng ký
        url = "https://www.13win16.com/home/<USER>"
        print(f"🌐 Đang điều hướng đến: {url}")
        
        driver.get(url)
        print("✅ Đã điều hướng thành công")
        
        # Chờ trang load
        print("⏳ Chờ trang load...")
        time.sleep(3)
        
        # Thông tin test
        test_account = {
            'username': 'taolatrumnohu1',
            'password': 'Test123456',
            'full_name': 'Nguyễn Văn A'
        }
        
        print(f"📝 Test với tài khoản: {test_account['username']}")
        
        # Selector cố định dựa trên debug
        selectors = {
            'username': 'input[placeholder*="điện thoại"]',
            'password': 'input[placeholder*="Mật khẩu"]:not([placeholder*="xác nhận"])',
            'confirm_password': 'input[placeholder*="xác nhận"]',
            'real_name': 'input[type="text"]:nth-of-type(4)',  # Input thứ 4
            'submit_button': 'button'
        }
        
        # Điền form
        success_count = 0
        
        # 1. Username
        try:
            print("📝 Điền username...")
            element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, selectors['username']))
            )
            element.clear()
            element.send_keys(test_account['username'])
            print(f"✅ Đã điền username: {test_account['username']}")
            success_count += 1
        except Exception as e:
            print(f"❌ Lỗi khi điền username: {e}")
        
        # 2. Password
        try:
            print("🔒 Điền password...")
            element = driver.find_element(By.CSS_SELECTOR, selectors['password'])
            element.clear()
            element.send_keys(test_account['password'])
            print("✅ Đã điền password")
            success_count += 1
        except Exception as e:
            print(f"❌ Lỗi khi điền password: {e}")
        
        # 3. Confirm Password
        try:
            print("🔒 Điền confirm password...")
            element = driver.find_element(By.CSS_SELECTOR, selectors['confirm_password'])
            element.clear()
            element.send_keys(test_account['password'])
            print("✅ Đã điền confirm password")
            success_count += 1
        except Exception as e:
            print(f"❌ Lỗi khi điền confirm password: {e}")
        
        # 4. Real Name (nếu có)
        try:
            print("👤 Điền họ tên...")
            element = driver.find_element(By.CSS_SELECTOR, selectors['real_name'])
            element.clear()
            element.send_keys(test_account['full_name'])
            print(f"✅ Đã điền họ tên: {test_account['full_name']}")
            success_count += 1
        except Exception as e:
            print(f"⚠️  Không tìm thấy trường họ tên: {e}")
        
        # 5. Checkbox (nếu có)
        try:
            print("☑️  Tìm checkbox...")
            checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
            if not checkbox.is_selected():
                checkbox.click()
                print("✅ Đã click checkbox")
                success_count += 1
        except Exception as e:
            print(f"⚠️  Không tìm thấy checkbox: {e}")
        
        # Chụp screenshot trước khi submit
        try:
            driver.save_screenshot("test_before_submit.png")
            print("📸 Đã chụp screenshot: test_before_submit.png")
        except:
            pass
        
        # 6. Submit (chỉ test, không thực sự submit)
        print("\n🎯 Tìm nút submit...")
        try:
            submit_button = driver.find_element(By.CSS_SELECTOR, selectors['submit_button'])
            print(f"✅ Tìm thấy nút submit: {submit_button.tag_name}")
            print(f"📝 Text: {submit_button.text}")
            print(f"🔗 Type: {submit_button.get_attribute('type')}")
            
            # Không click submit để tránh spam
            print("⚠️  Không submit để tránh spam website")
            
        except Exception as e:
            print(f"❌ Không tìm thấy nút submit: {e}")
        
        # Tóm tắt
        print(f"\n📊 Kết quả test:")
        print(f"✅ Điền thành công: {success_count} trường")
        print(f"🎯 Form có thể hoạt động: {'Có' if success_count >= 3 else 'Không'}")
        
        return success_count >= 3
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print("🔒 Đã đóng browser")
            except:
                pass

def main():
    """Main function"""
    try:
        success = test_registration()
        
        if success:
            print(f"\n{Fore.GREEN}🎉 TEST THÀNH CÔNG!{Style.RESET_ALL}")
            print("Form đăng ký có thể hoạt động với tool chính")
            print("\nBước tiếp theo:")
            print("1. Chạy tool chính với số lượng ít (1-2 tài khoản)")
            print("2. Theo dõi kết quả")
        else:
            print(f"\n{Fore.RED}❌ TEST THẤT BẠI!{Style.RESET_ALL}")
            print("Cần điều chỉnh selector hoặc kiểm tra website")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
