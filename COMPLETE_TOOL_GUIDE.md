# 🎯 TOOL HOÀN CHỈNH 13WIN - ĐĂNG KÝ + NHẬN THƯỞNG

## 🚀 Tính năng chính

✅ **Đăng ký tài khoản tự động**  
✅ **Nhận thưởng cơ bản tự động** (13 + 4 = 17 D)  
✅ **Hướng dẫn nhận thưởng app** (+35 D)  
✅ **Tổng có thể nhận: 52 D/tài khoản**  
✅ **3 chế độ hoạt động linh hoạt**  

## 📁 File chính

**`complete_registration_tool.py`** - Tool hoàn chỉnh ALL-IN-ONE

## 🎮 Cách sử dụng

### Chạy tool:
```bash
python complete_registration_tool.py
```

### Chọn chế độ:
```
🎯 TOOL HOÀN CHỈNH 13WIN - ĐĂNG KÝ + NHẬN THƯỞNG
============================================================
Chọn chế độ:
1. Đăng ký hàng loạt + nhận thưởng
2. Đăng ký 1 tài khoản + nhận thưởng
3. Chỉ nhận thưởng (tài khoản đã có)

Chọn (1/2/3):
```

## 🔧 Chế độ 1: Đăng ký hàng loạt

### Tính năng:
- Đăng ký nhiều tài khoản tuần tự
- Username: `taolatrumnohu1`, `taolatrumnohu2`, ...
- Họ tên thật: `TRAN HOANG AN` (cố định)
- Password: Ngẫu nhiên cho mỗi tài khoản
- Tự động nhận thưởng sau mỗi lần đăng ký

### Quy trình:
1. **Nhập số lượng** tài khoản cần đăng ký
2. **Nhập số bắt đầu** (mặc định: 1)
3. **Xác nhận** thông tin
4. **Tự động đăng ký** từng tài khoản
5. **Tự động nhận thưởng** cơ bản
6. **Hướng dẫn nhận thưởng app** (tùy chọn)

### Ví dụ:
```
📝 Nhập số lượng tài khoản cần đăng ký: 5
📝 Bắt đầu từ số (mặc định 1): 1

📋 Sẽ đăng ký 5 tài khoản:
👤 Username: taolatrumnohu1 → taolatrumnohu5
🏦 Họ tên thật: TRAN HOANG AN
🔒 Password: Ngẫu nhiên cho mỗi tài khoản

Tiếp tục? (y/n): y
```

## 🎯 Chế độ 2: Đăng ký 1 tài khoản

### Tính năng:
- Đăng ký 1 tài khoản duy nhất
- Tự nhập username, password, họ tên
- Tự động nhận thưởng sau đăng ký

### Ví dụ:
```
👤 Username: myusername123
🔒 Password: mypassword123
👨 Họ tên thật (mặc định: TRAN HOANG AN): TRAN HOANG AN
```

## 🎁 Chế độ 3: Chỉ nhận thưởng

### Tính năng:
- Dành cho tài khoản đã có sẵn
- Chỉ đăng nhập và nhận thưởng
- Không đăng ký mới

### Ví dụ:
```
👤 Username: taolatrumnohu1
🔒 Password: abc123def456
```

## 🎁 Hệ thống thưởng

### Thưởng tự động (17 D):
| Nhiệm vụ | Thưởng | Trạng thái |
|----------|--------|------------|
| **Đăng ký tài khoản** | 13.00 D | ✅ Tự động |
| **Cài đặt phương thức liên lạc** | 4.00 D | ✅ Tự động |

### Thưởng app (35 D):
| Nhiệm vụ | Thưởng | Trạng thái |
|----------|--------|------------|
| **Tải xuống, đăng nhập** | 35.00 D | 📱 Cần app |

### Tổng thưởng: **52 D/tài khoản**

## 📱 Hướng dẫn nhận thưởng app

### Sau khi đăng ký thành công:
1. **Tool sẽ hỏi**: "Có muốn làm nhiệm vụ app ngay không? (y/n)"
2. **Chọn y** để xem hướng dẫn
3. **Tool hiển thị**:
   ```
   📱 Hướng dẫn:
   👤 Username: taolatrumnohu1
   🔒 Password: abc123def456
   1. Mở app 13win
   2. Đăng nhập với thông tin trên
   3. Nhấn Enter khi hoàn thành
   ```
4. **Làm theo hướng dẫn** trên điện thoại/BlueStacks
5. **Nhấn Enter** khi hoàn thành
6. **Tool tự động kiểm tra** và nhận thưởng

## 📊 Kết quả mong đợi

### Sau khi hoàn thành:
```
📊 KẾT QUẢ CUỐI CÙNG:
✅ Thành công: 5
❌ Thất bại: 0
💰 Ước tính đã nhận: 85 D (chưa tính thưởng app)
📁 Tài khoản thành công đã lưu trong: successful_accounts.txt
```

### File `successful_accounts.txt`:
```
Username: taolatrumnohu1
Password: abc123def456
Full Name: TRAN HOANG AN
Time: 2024-01-15 14:30:25
--------------------------------------------------
Username: taolatrumnohu2
Password: xyz789ghi012
Full Name: TRAN HOANG AN
Time: 2024-01-15 14:35:30
--------------------------------------------------
```

## 🔧 Yêu cầu hệ thống

- ✅ Python 3.7+
- ✅ Chrome browser
- ✅ ChromeDriver trong thư mục `drivers/`
- ✅ Packages: selenium, colorama

### Cài đặt dependencies:
```bash
pip install selenium colorama
```

## 🚨 Lưu ý quan trọng

### Về tốc độ:
- Tool có **delay ngẫu nhiên** 5-10s giữa các tài khoản
- **Không spam** quá nhanh để tránh bị phát hiện

### Về lỗi:
- Tool sẽ **chụp screenshot** khi có lỗi
- Kiểm tra file `.png` để debug

### Về tài khoản:
- **Họ tên thật** phải khớp với chủ tài khoản ngân hàng
- **Username** phải duy nhất, không trùng lặp

## 🎯 Tips sử dụng hiệu quả

1. **Bắt đầu với số lượng nhỏ** (2-3 tài khoản) để test
2. **Kiểm tra kết quả** trước khi chạy số lượng lớn
3. **Backup file** `successful_accounts.txt` thường xuyên
4. **Chạy vào giờ ít người** để tránh quá tải server
5. **Chuẩn bị sẵn app** trên điện thoại để nhận thưởng nhanh

## 🆘 Troubleshooting

### Lỗi không tìm thấy ChromeDriver:
```
❌ Lỗi khởi tạo driver: 'chromedriver' executable needs to be in PATH
```
**Giải pháp**: Tải ChromeDriver và đặt trong thư mục `drivers/`

### Lỗi không tìm thấy element:
```
❌ Lỗi nhập username: Message: no such element
```
**Giải pháp**: Website có thể đã thay đổi giao diện, cần cập nhật selector

### Lỗi tài khoản đã tồn tại:
```
⚠️ Tài khoản đã tồn tại!
```
**Giải pháp**: Thay đổi số bắt đầu hoặc prefix username

## 🎉 Kết luận

Tool này giúp bạn:
- ✅ **Tiết kiệm thời gian** đăng ký thủ công
- ✅ **Tự động hóa** quy trình nhận thưởng
- ✅ **Tối đa hóa** số tiền thưởng nhận được
- ✅ **Quản lý** nhiều tài khoản dễ dàng

**Chúc bạn sử dụng tool hiệu quả! 🎁**
