"""
Test kết nối với Chrome debug mode
"""

import os
import sys
from browser_manager import BrowserManager
from config import BROWSER_CONFIG

def test_chrome_connection():
    """Test kết nối với Chrome debug"""
    print("🧪 TEST KẾT NỐI CHROME DEBUG MODE")
    print("="*50)
    
    # Cập nhật config để sử dụng existing browser
    BROWSER_CONFIG['use_existing_browser'] = True
    
    print("🔗 Đang thử kết nối với Chrome debug...")
    
    try:
        # Tạo browser manager
        browser = BrowserManager()
        
        # Thử kết nối
        if browser.create_browser():
            print("✅ Kết nối Chrome debug thành công!")
            
            # Test điều hướng
            print("🌐 Test điều hướng đến Google...")
            if browser.navigate_to_url("https://www.google.com"):
                print("✅ Điều hướng thành công!")
                
                # Lấy title
                try:
                    title = browser.driver.title
                    print(f"📄 Title trang: {title}")
                except:
                    print("⚠️  Không thể lấy title")
                
                # Test điều hướng đến 13win16
                print("🎯 Test điều hướng đến 13win16...")
                if browser.navigate_to_url("https://www.13win16.com/home/<USER>"):
                    print("✅ Điều hướng đến 13win16 thành công!")
                    
                    # Chờ trang load
                    import time
                    time.sleep(3)
                    
                    try:
                        title = browser.driver.title
                        print(f"📄 Title trang 13win16: {title}")
                    except:
                        print("⚠️  Không thể lấy title 13win16")
                else:
                    print("❌ Không thể điều hướng đến 13win16")
            else:
                print("❌ Không thể điều hướng đến Google")
            
            # Đóng browser
            browser.close_browser()
            print("✅ Test hoàn thành!")
            
        else:
            print("❌ Không thể kết nối với Chrome debug!")
            print("\n📋 Hướng dẫn khắc phục:")
            print("1. Chạy: python start_chrome_debug.py")
            print("2. Đảm bảo Chrome debug đang chạy trên port 9222")
            print("3. Thử lại test này")
            
    except Exception as e:
        print(f"❌ Lỗi khi test: {e}")
        print("\n📋 Hướng dẫn khắc phục:")
        print("1. Chạy: python start_chrome_debug.py")
        print("2. Đảm bảo Chrome debug đang chạy")
        print("3. Kiểm tra port 9222 không bị chặn")

def main():
    """Main function"""
    try:
        test_chrome_connection()
    except KeyboardInterrupt:
        print("\n👋 Test bị hủy!")
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")
    
    input("\nNhấn Enter để thoát...")

if __name__ == "__main__":
    main()
