"""
Script cài đặt các dependencies cần thiết cho BlueStacks automation
"""

import subprocess
import sys
import os

def install_package(package):
    """Cài đặt package qua pip"""
    try:
        print(f"📦 Cài đặt {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ Đã cài đặt {package}")
        return True
    except Exception as e:
        print(f"❌ Lỗi cài đặt {package}: {e}")
        return False

def main():
    """Main function"""
    print("🔧 CÀI ĐẶT DEPENDENCIES CHO BLUESTACKS AUTOMATION")
    print("="*60)
    
    # Danh sách packages cần thiết
    packages = [
        "pyautogui",
        "opencv-python", 
        "pillow",
        "selenium",
        "colorama"
    ]
    
    print("📋 Các package sẽ được cài đặt:")
    for pkg in packages:
        print(f"  - {pkg}")
    
    confirm = input(f"\nTiếp tục cài đặt? (y/n): ").lower()
    
    if confirm == 'y':
        success_count = 0
        
        for package in packages:
            if install_package(package):
                success_count += 1
        
        print(f"\n📊 KẾT QUẢ:")
        print(f"✅ Thành công: {success_count}/{len(packages)}")
        
        if success_count == len(packages):
            print("🎉 Tất cả dependencies đã được cài đặt!")
        else:
            print("⚠️  Một số packages không cài đặt được")
    else:
        print("❌ Đã hủy cài đặt")

if __name__ == "__main__":
    main()
