@echo off
echo 🔨 Building 13Win Account Manager...
echo.

REM Check if .NET 6 is installed
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ .NET 6.0 is not installed!
    echo Please install .NET 6.0 from: https://dotnet.microsoft.com/download/dotnet/6.0
    pause
    exit /b 1
)

echo ✅ .NET version:
dotnet --version
echo.

REM Restore packages
echo 📦 Restoring packages...
dotnet restore
if errorlevel 1 (
    echo ❌ Failed to restore packages!
    pause
    exit /b 1
)

REM Build project
echo 🔨 Building project...
dotnet build -c Release
if errorlevel 1 (
    echo ❌ Build failed!
    pause
    exit /b 1
)

REM Publish self-contained executable
echo 📦 Publishing executable...
dotnet publish -c Release -r win-x64 --self-contained -p:PublishSingleFile=true -p:PublishTrimmed=true
if errorlevel 1 (
    echo ❌ Publish failed!
    pause
    exit /b 1
)

echo.
echo ✅ Build completed successfully!
echo 📁 Executable location: bin\Release\net6.0-windows\win-x64\publish\AccountManager.exe
echo.
echo 🚀 You can now run AccountManager.exe
pause
