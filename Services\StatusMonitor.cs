using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using AccountManager.Models;

namespace AccountManager.Services
{
    public class StatusMonitor : IDisposable
    {
        private readonly AccountService _accountService;
        private readonly FileSystemWatcher _fileWatcher;
        private readonly Timer _timer;
        private bool _disposed = false;

        public event EventHandler<string>? StatusChanged;

        public StatusMonitor(AccountService accountService)
        {
            _accountService = accountService;

            // Monitor account_status.json file changes
            _fileWatcher = new FileSystemWatcher();
            _fileWatcher.Path = Directory.GetCurrentDirectory();
            _fileWatcher.Filter = "account_status.json";
            _fileWatcher.NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size;
            _fileWatcher.Changed += OnFileChanged;
            _fileWatcher.EnableRaisingEvents = true;

            // Monitor Python tool output
            _timer = new Timer(MonitorPythonTool, null, TimeSpan.Zero, TimeSpan.FromSeconds(2));
        }

        private void OnFileChanged(object sender, FileSystemEventArgs e)
        {
            try
            {
                // Delay to ensure file is fully written
                Thread.Sleep(500);
                
                StatusChanged?.Invoke(this, "Account status file updated");
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error monitoring file: {ex.Message}");
            }
        }

        private void MonitorPythonTool(object? state)
        {
            try
            {
                // Check if Python tool is running
                var processes = System.Diagnostics.Process.GetProcessesByName("python");
                var isRunning = processes.Length > 0;

                if (isRunning)
                {
                    // Check for screenshot files to detect progress
                    var screenshotDir = "screenshots";
                    if (Directory.Exists(screenshotDir))
                    {
                        var files = Directory.GetFiles(screenshotDir, "*.png");
                        var latestFile = "";
                        DateTime latestTime = DateTime.MinValue;

                        foreach (var file in files)
                        {
                            var fileTime = File.GetLastWriteTime(file);
                            if (fileTime > latestTime)
                            {
                                latestTime = fileTime;
                                latestFile = file;
                            }
                        }

                        if (!string.IsNullOrEmpty(latestFile) && 
                            DateTime.Now - latestTime < TimeSpan.FromMinutes(1))
                        {
                            var fileName = Path.GetFileNameWithoutExtension(latestFile);
                            StatusChanged?.Invoke(this, $"Tool progress: {fileName}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Ignore monitoring errors
                Console.WriteLine($"Monitor error: {ex.Message}");
            }
        }

        public async Task<bool> WaitForToolCompletion(int timeoutMinutes = 30)
        {
            var startTime = DateTime.Now;
            var timeout = TimeSpan.FromMinutes(timeoutMinutes);

            while (DateTime.Now - startTime < timeout)
            {
                var processes = System.Diagnostics.Process.GetProcessesByName("python");
                if (processes.Length == 0)
                {
                    // Python tool finished
                    return true;
                }

                await Task.Delay(5000); // Check every 5 seconds
            }

            return false; // Timeout
        }

        public void UpdateAccountFromScreenshot(string screenshotPath)
        {
            try
            {
                var fileName = Path.GetFileNameWithoutExtension(screenshotPath);
                var parts = fileName.Split('_');

                if (parts.Length >= 3)
                {
                    var stepNumber = parts[0];
                    var username = parts[1];
                    var action = parts[2];

                    AccountStatus status = action.ToLower() switch
                    {
                        "registration" => AccountStatus.Registering,
                        "success" => AccountStatus.RegisterSuccess,
                        "failed" => AccountStatus.RegisterFailed,
                        "task" => AccountStatus.WebRewardClaimed,
                        "claim" => AccountStatus.AppRewardClaimed,
                        _ => AccountStatus.NotStarted
                    };

                    _accountService.UpdateAccountStatus(username, status, $"Screenshot: {fileName}");
                }
            }
            catch (Exception ex)
            {
                StatusChanged?.Invoke(this, $"Error parsing screenshot: {ex.Message}");
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _fileWatcher?.Dispose();
                _timer?.Dispose();
                _disposed = true;
            }
        }
    }
}
