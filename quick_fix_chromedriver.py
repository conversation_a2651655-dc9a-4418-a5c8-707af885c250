"""
Quick fix ChromeDriver cho Chrome 136
"""

import os
import requests
import zipfile
import shutil
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def quick_fix():
    """Fix nhanh ChromeDriver cho Chrome 136"""
    print("🔧 QUICK FIX CHROMEDRIVER CHO CHROME 136")
    print("="*50)
    
    try:
        # Xóa cache WebDriverManager cũ
        print("🧹 Xóa cache WebDriverManager...")
        cache_dir = os.path.expanduser("~/.wdm")
        if os.path.exists(cache_dir):
            shutil.rmtree(cache_dir)
            print("✅ Đã xóa cache WebDriverManager")
        
        # Tạo thư mục drivers
        drivers_dir = "drivers"
        os.makedirs(drivers_dir, exist_ok=True)
        
        # Xóa ChromeDriver cũ
        old_chromedriver = os.path.join(drivers_dir, "chromedriver.exe")
        if os.path.exists(old_chromedriver):
            os.remove(old_chromedriver)
            print("🗑️  Đã xóa ChromeDriver cũ")
        
        # Tải ChromeDriver 136
        version = "136.0.7103.113"
        url = f"https://storage.googleapis.com/chrome-for-testing-public/{version}/win64/chromedriver-win64.zip"
        
        print(f"📥 Đang tải ChromeDriver {version}...")
        
        response = requests.get(url, stream=True, timeout=30)
        if response.status_code == 200:
            zip_path = os.path.join(drivers_dir, "chromedriver.zip")
            
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print("✅ Đã tải xong")
            
            # Giải nén
            print("📦 Đang giải nén...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(drivers_dir)
            
            # Di chuyển file
            extracted_dir = os.path.join(drivers_dir, "chromedriver-win64")
            if os.path.exists(extracted_dir):
                chromedriver_exe = os.path.join(extracted_dir, "chromedriver.exe")
                if os.path.exists(chromedriver_exe):
                    shutil.move(chromedriver_exe, os.path.join(drivers_dir, "chromedriver.exe"))
                    shutil.rmtree(extracted_dir)
            
            # Xóa file zip
            os.remove(zip_path)
            
            # Kiểm tra
            chromedriver_path = os.path.join(drivers_dir, "chromedriver.exe")
            if os.path.exists(chromedriver_path):
                print(f"✅ ChromeDriver 136 đã sẵn sàng: {chromedriver_path}")
                
                # Test
                import subprocess
                try:
                    result = subprocess.run([chromedriver_path, "--version"], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        print(f"✅ Version: {result.stdout.strip()}")
                        print(f"\n{Fore.GREEN}🎉 QUICK FIX THÀNH CÔNG!{Style.RESET_ALL}")
                        print(f"{Fore.YELLOW}💡 Bây giờ có thể chạy tool bình thường{Style.RESET_ALL}")
                        return True
                except:
                    pass
            
            print(f"{Fore.RED}❌ Có lỗi trong quá trình fix{Style.RESET_ALL}")
            return False
        else:
            print(f"❌ Không thể tải: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def main():
    """Main function"""
    try:
        if quick_fix():
            print(f"\n{Fore.GREEN}✅ HOÀN THÀNH!{Style.RESET_ALL}")
            print("Bước tiếp theo:")
            print("1. Chạy: python clean_start.py")
            print("2. Hoặc: python main.py")
        else:
            print(f"\n{Fore.RED}❌ QUICK FIX THẤT BẠI{Style.RESET_ALL}")
            print("Thử:")
            print("1. Chạy với quyền Administrator")
            print("2. Tắt antivirus tạm thời")
            print("3. Kiểm tra kết nối internet")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
