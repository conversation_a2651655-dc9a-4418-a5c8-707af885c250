"""
TOOL ĐĂNG KÝ ĐỠN GIẢN - NHIỀU CHROME
Fix lỗi thông báo và mỗi tài khoản = 1 Chrome riêng
"""

import time
import random
import string
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class SimpleTool:
    def __init__(self):
        self.chromedriver_path = "drivers/chromedriver.exe"
        self.screenshot_folder = "chụp màn hình"
        self.active_drivers = {}
        
        # Tạo folder screenshot
        if not os.path.exists(self.screenshot_folder):
            os.makedirs(self.screenshot_folder)
            print(f"📁 Đã tạo folder: {self.screenshot_folder}")
    
    def create_simple_chrome(self, username):
        """Tạo Chrome đơn giản cho tài khoản"""
        try:
            chrome_options = Options()
            
            # Chỉ các option cần thiết
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            
            # Tắt logging để giảm lỗi
            chrome_options.add_argument('--log-level=3')
            chrome_options.add_argument('--silent')
            
            # Profile đơn giản
            profile_path = f"temp_profile_{username}_{int(time.time())}"
            chrome_options.add_argument(f'--user-data-dir={profile_path}')
            
            service = Service(self.chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(3)
            
            self.active_drivers[username] = driver
            print(f"✅ Chrome cho {username}")
            return driver
            
        except Exception as e:
            print(f"❌ Lỗi tạo Chrome: {e}")
            return None
    
    def save_screenshot(self, filename, driver):
        """Chụp screenshot"""
        try:
            filepath = os.path.join(self.screenshot_folder, filename)
            driver.save_screenshot(filepath)
            print(f"📸 {filename}")
        except:
            pass
    
    def close_popups(self, driver, username):
        """Đóng popup đơn giản"""
        try:
            print("🔔 Đóng popup...")
            time.sleep(3)
            
            # Thử các cách đóng popup
            for _ in range(5):
                try:
                    # Thử click OK
                    ok_btn = driver.find_element(By.XPATH, "//button[contains(text(), 'OK')]")
                    if ok_btn.is_displayed():
                        ok_btn.click()
                        print("✅ Đã đóng OK")
                        time.sleep(1)
                        continue
                except:
                    pass
                
                try:
                    # Thử click Đóng
                    close_btn = driver.find_element(By.XPATH, "//button[contains(text(), 'Đóng')]")
                    if close_btn.is_displayed():
                        close_btn.click()
                        print("✅ Đã đóng popup")
                        time.sleep(1)
                        continue
                except:
                    pass
                
                try:
                    # Thử ESC
                    driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                    print("✅ Đã nhấn ESC")
                    time.sleep(1)
                except:
                    pass
                
                break
            
            return True
        except:
            return False
    
    def register_simple(self, username, password, fullname):
        """Đăng ký đơn giản"""
        try:
            print(f"\n🚀 ĐĂNG KÝ: {username}")
            
            # Tạo Chrome
            driver = self.create_simple_chrome(username)
            if not driver:
                return False
            
            # Đi đến trang đăng ký
            driver.get("https://www.13win16.com/home/<USER>")
            time.sleep(8)
            
            self.save_screenshot(f"01_{username}_register.png", driver)
            
            # Tìm input
            inputs = driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
            print(f"📝 Tìm thấy {len(inputs)} input")
            
            if len(inputs) < 3:
                print("❌ Không đủ input")
                return False
            
            # Điền form
            try:
                inputs[0].clear()
                inputs[0].send_keys(username)
                time.sleep(1)
                
                inputs[1].clear()
                inputs[1].send_keys(password)
                time.sleep(1)
                
                if len(inputs) >= 3:
                    inputs[2].clear()
                    inputs[2].send_keys(password)
                    time.sleep(1)
                
                if len(inputs) >= 4:
                    inputs[3].clear()
                    inputs[3].send_keys(fullname)
                    time.sleep(1)
                
                print("✅ Đã điền form")
            except Exception as e:
                print(f"❌ Lỗi điền: {e}")
                return False
            
            self.save_screenshot(f"02_{username}_filled.png", driver)
            
            # Checkbox
            try:
                checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Checkbox")
            except:
                pass
            
            # Submit
            try:
                buttons = driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    buttons[-1].click()
                    print("🚀 Submit")
                    time.sleep(8)
                else:
                    print("❌ Không có button")
                    return False
            except Exception as e:
                print(f"❌ Lỗi submit: {e}")
                return False
            
            self.save_screenshot(f"03_{username}_result.png", driver)
            
            # Kiểm tra kết quả
            page_source = driver.page_source.lower()
            
            if any(keyword in page_source for keyword in ["thành công", "success", "welcome"]):
                print(f"{Fore.GREEN}🎉 THÀNH CÔNG!{Style.RESET_ALL}")
                
                # Đóng popup
                self.close_popups(driver, username)
                self.save_screenshot(f"04_{username}_after_popup.png", driver)
                
                # Chuyển trang thưởng
                try:
                    driver.get("https://www.13win16.com/home/<USER>")
                    time.sleep(3)
                    self.save_screenshot(f"05_{username}_reward_page.png", driver)
                    print("✅ Đã chuyển trang thưởng")
                except:
                    print("⚠️  Không chuyển được trang thưởng")
                
                # Lưu tài khoản
                with open("accounts.txt", "a", encoding="utf-8") as f:
                    f.write(f"{username}|{password}|{fullname}\n")
                
                return True
                
            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists"]):
                print(f"{Fore.YELLOW}⚠️  Đã tồn tại{Style.RESET_ALL}")
                return False
            else:
                print(f"{Fore.RED}❓ Không rõ{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi: {e}")
            return False
    
    def generate_password(self):
        """Tạo password"""
        return ''.join(random.choice(string.ascii_letters + string.digits) for _ in range(8)) + "123"
    
    def run_batch(self):
        """Chạy hàng loạt"""
        print("🚀 TOOL ĐĂNG KÝ ĐƠN GIẢN - NHIỀU CHROME")
        print("="*50)
        
        # Input
        prefix = input("👤 Prefix: ").strip() or "taolatrumnohu"
        try:
            count = int(input("📝 Số lượng: "))
            start = int(input("📝 Bắt đầu từ: ") or "1")
        except:
            print("❌ Số không hợp lệ!")
            return
        
        fullname = "TRAN HOANG AN"
        
        print(f"\n📋 Đăng ký {count} tài khoản:")
        print(f"👤 {prefix}{start} → {prefix}{start+count-1}")
        print(f"👨 {fullname}")
        
        if input("Tiếp tục? (y/n): ").lower() != 'y':
            return
        
        success = 0
        failed = 0
        
        try:
            for i in range(count):
                num = start + i
                username = f"{prefix}{num}"
                password = self.generate_password()
                
                print(f"\n📊 {i+1}/{count}: {username}")
                
                if self.register_simple(username, password, fullname):
                    success += 1
                    print(f"{Fore.GREEN}✅ Thành công: {success}{Style.RESET_ALL}")
                else:
                    failed += 1
                    print(f"{Fore.RED}❌ Thất bại: {failed}{Style.RESET_ALL}")
                
                # Delay
                if i < count - 1:
                    delay = random.randint(3, 8)
                    print(f"⏳ Chờ {delay}s...")
                    time.sleep(delay)
            
            print(f"\n📊 KẾT QUẢ:")
            print(f"✅ Thành công: {success}")
            print(f"❌ Thất bại: {failed}")
            print(f"🌐 Chrome đang chạy: {len(self.active_drivers)}")
            print(f"📁 Tài khoản: accounts.txt")
            
            if success > 0:
                print(f"\n💰 MỖI TÀI KHOẢN CÓ ~22D")
                print(f"📱 CẦN ĐĂNG NHẬP APP ĐỂ NHẬN THÊM 35D")
                print(f"🎯 MỤC TIÊU: 52D/TÀI KHOẢN")
                
                print(f"\n{Fore.YELLOW}HƯỚNG DẪN:{Style.RESET_ALL}")
                print("1. Mở app 13win trên điện thoại")
                print("2. Đăng nhập từng tài khoản")
                print("3. Nhận thưởng app (+35D)")
                print("4. Khi đủ 52D, có thể đóng Chrome")
            
        except KeyboardInterrupt:
            print(f"\n⚠️  Đã dừng!")
        
        finally:
            choice = input(f"\n{Fore.CYAN}Đóng tất cả Chrome? (y/n): {Style.RESET_ALL}").lower()
            if choice == 'y':
                self.close_all()
    
    def close_all(self):
        """Đóng tất cả Chrome"""
        for username, driver in self.active_drivers.items():
            try:
                driver.quit()
                print(f"✅ Đóng {username}")
            except:
                pass
        self.active_drivers.clear()

def main():
    try:
        tool = SimpleTool()
        tool.run_batch()
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
