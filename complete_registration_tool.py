"""
TOOL HOÀN CHỈNH: <PERSON><PERSON><PERSON> ký tài kho<PERSON>n + <PERSON><PERSON><PERSON><PERSON> thưởng tự động
Tích hợp tất cả tính năng trong 1 tool duy nhất
"""

import time
import random
import string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class Complete13WinTool:
    def __init__(self):
        self.driver = None
        self.chromedriver_path = "drivers/chromedriver.exe"
        self.register_url = "https://www.13win16.com/home/<USER>"
        self.login_url = "https://www.13win16.com/home/<USER>"
        self.task_url = "https://www.13win16.com/home/<USER>"
        self.successful_accounts = []

    def setup_driver(self):
        """Khởi tạo Chrome driver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            service = Service(self.chromedriver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(5)

            print("✅ Chrome driver đã khởi tạo")
            return True

        except Exception as e:
            print(f"❌ Lỗi khởi tạo driver: {e}")
            return False

    def generate_password(self):
        """Tạo mật khẩu ngẫu nhiên"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(10)) + "123"

    def register_account(self, username, password, full_name):
        """Đăng ký tài khoản"""
        try:
            print(f"\n🚀 ĐĂNG KÝ TÀI KHOẢN: {username}")
            print("="*50)

            # Điều hướng đến trang đăng ký
            self.driver.get(self.register_url)
            time.sleep(3)

            # Selector form đăng ký
            selectors = {
                'username': 'input[placeholder*="điện thoại"], input[placeholder*="username"], input[type="text"]:not([placeholder*="Họ"])',
                'password': 'input[placeholder*="Mật khẩu"]:not([placeholder*="xác nhận"])',
                'confirm_password': 'input[placeholder*="xác nhận"], input[placeholder*="Nhập lại"]',
                'real_name': 'input[placeholder="Họ Tên Thật"], input[placeholder*="Họ"], input[placeholder*="tên"]',
                'submit_button': 'button[type="submit"], button:contains("Đăng ký"), .register-btn'
            }

            # 1. Username
            try:
                element = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, selectors['username']))
                )
                element.clear()
                element.send_keys(username)
                print(f"✅ Username: {username}")
                time.sleep(1)
            except Exception as e:
                print(f"❌ Lỗi nhập username: {e}")
                return False

            # 2. Password
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selectors['password'])
                element.clear()
                element.send_keys(password)
                print(f"✅ Password: {password}")
                time.sleep(1)
            except Exception as e:
                print(f"❌ Lỗi nhập password: {e}")
                return False

            # 3. Confirm Password
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selectors['confirm_password'])
                element.clear()
                element.send_keys(password)
                print("✅ Confirm Password")
                time.sleep(1)
            except Exception as e:
                print(f"❌ Lỗi nhập confirm password: {e}")
                return False

            # 4. Họ tên thật
            try:
                element = self.driver.find_element(By.CSS_SELECTOR, selectors['real_name'])
                element.clear()
                element.send_keys(full_name)
                print(f"✅ Họ tên thật: {full_name}")
                time.sleep(1)
            except Exception as e:
                print(f"❌ Lỗi nhập họ tên: {e}")
                return False

            # 5. Checkbox (nếu có)
            try:
                checkbox = self.driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Checkbox")
            except:
                print("⚠️  Không tìm thấy checkbox")

            # 6. Submit
            try:
                submit_button = self.driver.find_element(By.CSS_SELECTOR, selectors['submit_button'])
                submit_button.click()
                print("🚀 Đã submit đăng ký...")
                time.sleep(5)
            except Exception as e:
                print(f"❌ Lỗi submit: {e}")
                return False

            # Kiểm tra kết quả
            page_source = self.driver.page_source.lower()

            if any(keyword in page_source for keyword in ["thành công", "success", "welcome", "chào mừng"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")

                # Lưu tài khoản thành công
                account_info = {
                    'username': username,
                    'password': password,
                    'full_name': full_name,
                    'time': time.strftime('%Y-%m-%d %H:%M:%S')
                }
                self.successful_accounts.append(account_info)

                # Lưu vào file
                with open("successful_accounts.txt", "a", encoding="utf-8") as f:
                    f.write(f"Username: {username}\n")
                    f.write(f"Password: {password}\n")
                    f.write(f"Full Name: {full_name}\n")
                    f.write(f"Time: {account_info['time']}\n")
                    f.write("-" * 50 + "\n")

                return True

            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already", "existed"]):
                print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                return False

            else:
                print(f"{Fore.RED}❓ Không xác định được kết quả đăng ký{Style.RESET_ALL}")
                # Chụp screenshot để debug
                try:
                    self.driver.save_screenshot(f"register_result_{username}.png")
                    print(f"📸 Đã chụp screenshot: register_result_{username}.png")
                except:
                    pass
                return False

        except Exception as e:
            print(f"{Fore.RED}❌ Lỗi đăng ký: {e}{Style.RESET_ALL}")
            return False

    def login_account(self, username, password):
        """Đăng nhập tài khoản"""
        try:
            print(f"\n🔑 ĐĂNG NHẬP: {username}")

            # Điều hướng đến trang đăng nhập
            self.driver.get(self.login_url)
            time.sleep(3)

            # Điền form đăng nhập
            username_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder*="điện thoại"], input[type="text"]'))
            )
            username_field.clear()
            username_field.send_keys(username)
            print(f"✅ Username: {username}")

            password_field = self.driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
            password_field.clear()
            password_field.send_keys(password)
            print(f"✅ Password: {password}")

            login_button = self.driver.find_element(By.CSS_SELECTOR, 'button[type="submit"], button')
            login_button.click()
            print("🚀 Đã submit đăng nhập...")

            time.sleep(5)

            # Kiểm tra đăng nhập thành công
            page_source = self.driver.page_source.lower()

            if any(keyword in page_source for keyword in ["dashboard", "profile", "logout", "đăng xuất", "tài khoản", "home"]):
                print(f"{Fore.GREEN}✅ ĐĂNG NHẬP THÀNH CÔNG!{Style.RESET_ALL}")
                return True
            else:
                print(f"{Fore.RED}❌ ĐĂNG NHẬP THẤT BẠI!{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"❌ Lỗi đăng nhập: {e}")
            return False

    def claim_rewards(self):
        """Nhận thưởng từ trang nhiệm vụ"""
        try:
            print(f"\n🎁 NHẬN THƯỞNG TỰ ĐỘNG")
            print("="*40)

            # Điều hướng đến trang nhiệm vụ
            self.driver.get(self.task_url)
            time.sleep(3)

            print(f"📄 URL: {self.task_url}")

            # Tìm tất cả nút "Nhận"
            claim_buttons = []

            # Các selector có thể cho nút "Nhận"
            selectors = [
                "//button[contains(text(), 'Nhận')]",
                "//button[contains(@class, 'green')]",
                "//button[contains(@class, 'success')]",
                "//div[contains(text(), 'Nhận')]/parent::button",
                "//span[contains(text(), 'Nhận')]/parent::button"
            ]

            print("🔍 Tìm các nút 'Nhận'...")

            for selector in selectors:
                try:
                    buttons = self.driver.find_elements(By.XPATH, selector)
                    for btn in buttons:
                        if btn not in claim_buttons and btn.is_displayed():
                            claim_buttons.append(btn)
                except:
                    pass

            print(f"📊 Tổng cộng tìm thấy: {len(claim_buttons)} nút có thể nhận")

            if not claim_buttons:
                print("⚠️  Không tìm thấy nút 'Nhận' nào")

                # Chụp screenshot để debug
                try:
                    self.driver.save_screenshot("no_claim_buttons.png")
                    print("📸 Đã chụp screenshot: no_claim_buttons.png")
                except:
                    pass

                return 0

            # Click từng nút "Nhận"
            claimed_count = 0

            for i, button in enumerate(claim_buttons):
                try:
                    # Kiểm tra nút có thể click không
                    if button.is_enabled() and button.is_displayed():
                        # Scroll đến nút
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                        time.sleep(1)

                        # Lấy text của nút để xác nhận
                        try:
                            button_text = button.text
                            print(f"🎯 Chuẩn bị click nút {i+1}: '{button_text}'")
                        except:
                            print(f"🎯 Chuẩn bị click nút {i+1}")

                        # Click nút
                        button.click()
                        claimed_count += 1

                        print(f"✅ Đã click nút {i+1}: Nhận thưởng")

                        # Chờ một chút giữa các lần click
                        time.sleep(random.uniform(2, 4))

                    else:
                        print(f"⚠️  Nút {i+1} không thể click (đã nhận hoặc chưa đủ điều kiện)")

                except Exception as e:
                    print(f"❌ Lỗi khi click nút {i+1}: {e}")

            print(f"\n📊 Kết quả nhận thưởng:")
            print(f"✅ Đã nhận: {claimed_count} phần thưởng")

            # Chụp screenshot sau khi nhận thưởng
            try:
                self.driver.save_screenshot("after_claim_rewards.png")
                print("📸 Đã chụp screenshot: after_claim_rewards.png")
            except:
                pass

            return claimed_count

        except Exception as e:
            print(f"❌ Lỗi khi nhận thưởng: {e}")
            return 0

    def complete_registration_and_rewards(self, username, password, full_name):
        """Hoàn thành quy trình đăng ký + nhận thưởng"""
        print(f"\n🎯 QUY TRÌNH HOÀN CHỈNH: {username}")
        print("="*60)

        try:
            # BƯỚC 1: Đăng ký tài khoản
            print("📝 BƯỚC 1: Đăng ký tài khoản")
            if not self.register_account(username, password, full_name):
                return False

            # Chờ một chút để hệ thống cập nhật
            print("⏳ Chờ hệ thống cập nhật...")
            time.sleep(5)

            # BƯỚC 2: Đăng nhập lại
            print("\n🔑 BƯỚC 2: Đăng nhập để nhận thưởng")
            if not self.login_account(username, password):
                print("⚠️  Không thể đăng nhập, thử nhận thưởng trực tiếp...")

            # BƯỚC 3: Nhận thưởng
            print("\n🎁 BƯỚC 3: Nhận thưởng tự động")
            claimed_count = self.claim_rewards()

            if claimed_count > 0:
                print(f"{Fore.GREEN}🎉 HOÀN THÀNH! Đã nhận {claimed_count} phần thưởng{Style.RESET_ALL}")

                # BƯỚC 4: Hướng dẫn nhiệm vụ app (tùy chọn)
                print(f"\n📱 BƯỚC 4: Nhiệm vụ 'Tải xuống, đăng nhập' (+35.00 D)")
                print(f"{Fore.YELLOW}💡 Để nhận thêm 35.00 D:{Style.RESET_ALL}")
                print("1. Mở app 13win trên điện thoại/BlueStacks")
                print("2. Đăng nhập với tài khoản vừa tạo")
                print("3. Quay lại web để nhận thưởng")

                app_choice = input(f"\n{Fore.CYAN}Có muốn làm nhiệm vụ app ngay không? (y/n): {Style.RESET_ALL}").lower()

                if app_choice == 'y':
                    print(f"\n📱 Hướng dẫn:")
                    print(f"👤 Username: {username}")
                    print(f"🔒 Password: {password}")
                    print("1. Mở app 13win")
                    print("2. Đăng nhập với thông tin trên")
                    print("3. Nhấn Enter khi hoàn thành")

                    input(f"\n{Fore.CYAN}Nhấn Enter sau khi đã đăng nhập app...{Style.RESET_ALL}")

                    # Refresh và nhận thưởng lại
                    print("🔄 Kiểm tra nhiệm vụ app...")
                    additional_rewards = self.claim_rewards()

                    if additional_rewards > 0:
                        print(f"{Fore.GREEN}🎁 Đã nhận thêm {additional_rewards} phần thưởng từ app!{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.YELLOW}⚠️  Chưa có thưởng mới (có thể cần chờ thêm){Style.RESET_ALL}")

                return True
            else:
                print(f"{Fore.YELLOW}⚠️  Đăng ký thành công nhưng chưa nhận được thưởng{Style.RESET_ALL}")
                return True

        except Exception as e:
            print(f"{Fore.RED}❌ Lỗi quy trình: {e}{Style.RESET_ALL}")
            return False

    def run_batch_registration(self):
        """Chạy đăng ký hàng loạt"""
        print("🚀 TOOL HOÀN CHỈNH - ĐĂNG KÝ + NHẬN THƯỞNG")
        print("="*60)
        print("🎯 Tính năng:")
        print("✅ Đăng ký tài khoản tự động")
        print("✅ Nhận thưởng cơ bản (13 + 4 = 17 D)")
        print("✅ Hướng dẫn nhận thưởng app (+35 D)")
        print("✅ Tổng có thể nhận: 52 D/tài khoản")

        # Cấu hình
        username_prefix = "taolatrumnohu"
        bank_name = "TRAN HOANG AN"

        # Nhập số lượng
        try:
            count = int(input("\n📝 Nhập số lượng tài khoản cần đăng ký: "))
            start_num = int(input("📝 Bắt đầu từ số (mặc định 1): ") or "1")
        except:
            print("❌ Số không hợp lệ!")
            return

        print(f"\n📋 Sẽ đăng ký {count} tài khoản:")
        print(f"👤 Username: {username_prefix}{start_num} → {username_prefix}{start_num + count - 1}")
        print(f"🏦 Họ tên thật: {bank_name}")
        print(f"🔒 Password: Ngẫu nhiên cho mỗi tài khoản")

        confirm = input(f"\n{Fore.YELLOW}Tiếp tục? (y/n): {Style.RESET_ALL}").lower()
        if confirm != 'y':
            print("❌ Đã hủy!")
            return

        # Khởi tạo driver
        if not self.setup_driver():
            return

        # Bắt đầu đăng ký
        success_count = 0
        failed_count = 0

        try:
            for i in range(count):
                current_num = start_num + i
                username = f"{username_prefix}{current_num}"
                password = self.generate_password()

                print(f"\n📊 Tiến trình: {i+1}/{count}")

                try:
                    if self.complete_registration_and_rewards(username, password, bank_name):
                        success_count += 1
                        print(f"{Fore.GREEN}✅ Thành công: {success_count}/{i+1}{Style.RESET_ALL}")
                    else:
                        failed_count += 1
                        print(f"{Fore.RED}❌ Thất bại: {failed_count}/{i+1}{Style.RESET_ALL}")
                except Exception as e:
                    failed_count += 1
                    print(f"{Fore.RED}❌ Lỗi: {e}{Style.RESET_ALL}")

                # Delay giữa các lần đăng ký
                if i < count - 1:  # Không delay ở lần cuối
                    delay = random.randint(5, 10)
                    print(f"⏳ Chờ {delay}s trước lần tiếp theo...")
                    time.sleep(delay)

            # Tóm tắt
            print(f"\n📊 KẾT QUẢ CUỐI CÙNG:")
            print(f"✅ Thành công: {success_count}")
            print(f"❌ Thất bại: {failed_count}")
            print(f"💰 Ước tính đã nhận: {success_count * 17} D (chưa tính thưởng app)")
            print(f"📁 Tài khoản thành công đã lưu trong: successful_accounts.txt")

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}⚠️  Đã dừng bởi người dùng!{Style.RESET_ALL}")

        finally:
            if self.driver:
                try:
                    input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                    self.driver.quit()
                except:
                    pass

    def run_single_account(self):
        """Chạy cho 1 tài khoản duy nhất"""
        print("🎯 TOOL HOÀN CHỈNH - TÀI KHOẢN ĐƠN LẺ")
        print("="*50)

        # Nhập thông tin
        username = input("👤 Username: ").strip()
        password = input("🔒 Password: ").strip()
        full_name = input("👨 Họ tên thật (mặc định: TRAN HOANG AN): ").strip() or "TRAN HOANG AN"

        if not username or not password:
            print("❌ Vui lòng nhập đầy đủ thông tin!")
            return

        # Khởi tạo driver
        if not self.setup_driver():
            return

        try:
            success = self.complete_registration_and_rewards(username, password, full_name)

            if success:
                print(f"\n{Fore.GREEN}🎉 HOÀN THÀNH THÀNH CÔNG!{Style.RESET_ALL}")
                print("✅ Đăng ký tài khoản")
                print("🎁 Nhận thưởng cơ bản")
                print("💡 Có hướng dẫn nhận thưởng app")
            else:
                print(f"\n{Fore.RED}❌ THẤT BẠI!{Style.RESET_ALL}")

        finally:
            if self.driver:
                try:
                    input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                    self.driver.quit()
                except:
                    pass

    def run_claim_only(self):
        """Chỉ nhận thưởng cho tài khoản đã có"""
        print("🎁 TOOL NHẬN THƯỞNG - TÀI KHOẢN ĐÃ CÓ")
        print("="*50)

        # Nhập thông tin
        username = input("👤 Username: ").strip()
        password = input("🔒 Password: ").strip()

        if not username or not password:
            print("❌ Vui lòng nhập đầy đủ thông tin!")
            return

        # Khởi tạo driver
        if not self.setup_driver():
            return

        try:
            # Đăng nhập
            if self.login_account(username, password):
                # Nhận thưởng
                claimed_count = self.claim_rewards()

                if claimed_count > 0:
                    print(f"{Fore.GREEN}🎁 Đã nhận {claimed_count} phần thưởng!{Style.RESET_ALL}")
                else:
                    print(f"{Fore.YELLOW}⚠️  Không có thưởng mới hoặc đã nhận hết{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}❌ Không thể đăng nhập!{Style.RESET_ALL}")

        finally:
            if self.driver:
                try:
                    input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                    self.driver.quit()
                except:
                    pass

def main():
    """Main function"""
    try:
        print("🎯 TOOL HOÀN CHỈNH 13WIN - ĐĂNG KÝ + NHẬN THƯỞNG")
        print("="*60)
        print("Chọn chế độ:")
        print("1. Đăng ký hàng loạt + nhận thưởng")
        print("2. Đăng ký 1 tài khoản + nhận thưởng")
        print("3. Chỉ nhận thưởng (tài khoản đã có)")

        choice = input("\nChọn (1/2/3): ").strip()

        tool = Complete13WinTool()

        if choice == "1":
            tool.run_batch_registration()
        elif choice == "2":
            tool.run_single_account()
        elif choice == "3":
            tool.run_claim_only()
        else:
            print("❌ Lựa chọn không hợp lệ!")

    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
