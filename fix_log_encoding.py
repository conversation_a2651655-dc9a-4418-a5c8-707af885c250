"""
Script sửa lỗi encoding của file log
"""

import os
import shutil
from datetime import datetime
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def fix_log_encoding():
    """Sửa lỗi encoding của file log"""
    print("🔧 SỬA LỖI ENCODING FILE LOG")
    print("="*40)
    
    log_files = [
        "registration.log",
        "output/registration.log",
        "logs/registration.log"
    ]
    
    for log_file in log_files:
        if os.path.exists(log_file):
            print(f"\n📁 Tìm thấy file: {log_file}")
            
            # Backup file cũ
            backup_file = f"{log_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            try:
                shutil.copy2(log_file, backup_file)
                print(f"✅ Đã backup: {backup_file}")
            except Exception as e:
                print(f"⚠️  Không thể backup: {e}")
            
            # Thử đọc với các encoding khác nhau
            content = None
            original_encoding = None
            
            encodings = ['utf-8', 'utf-8-sig', 'cp1252', 'latin1', 'ascii']
            
            for encoding in encodings:
                try:
                    with open(log_file, 'r', encoding=encoding, errors='replace') as f:
                        content = f.read()
                    original_encoding = encoding
                    print(f"✅ Đọc thành công với encoding: {encoding}")
                    break
                except Exception as e:
                    print(f"❌ Lỗi với {encoding}: {e}")
                    continue
            
            if content is not None:
                # Ghi lại với UTF-8
                try:
                    with open(log_file, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"✅ Đã ghi lại file với UTF-8")
                    
                    # Thêm header để đánh dấu encoding
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    header = f"# Log file - UTF-8 encoding - Fixed at {timestamp}\n"
                    
                    with open(log_file, 'r', encoding='utf-8') as f:
                        original_content = f.read()
                    
                    with open(log_file, 'w', encoding='utf-8') as f:
                        f.write(header + original_content)
                    
                    print(f"✅ Đã thêm header UTF-8")
                    
                except Exception as e:
                    print(f"❌ Lỗi khi ghi file: {e}")
            else:
                print(f"❌ Không thể đọc file {log_file}")
                
                # Tạo file mới
                try:
                    os.remove(log_file)
                    with open(log_file, 'w', encoding='utf-8') as f:
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        f.write(f"# New log file created at {timestamp}\n")
                        f.write(f"{timestamp} - INFO - Log file recreated with UTF-8 encoding\n")
                    print(f"✅ Đã tạo file log mới với UTF-8")
                except Exception as e:
                    print(f"❌ Lỗi khi tạo file mới: {e}")
    
    # Tạo file log mặc định nếu không có
    default_log = "registration.log"
    if not os.path.exists(default_log):
        try:
            with open(default_log, 'w', encoding='utf-8') as f:
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"# Log file created at {timestamp}\n")
                f.write(f"{timestamp} - INFO - Tool ready to start\n")
            print(f"\n✅ Đã tạo file log mặc định: {default_log}")
        except Exception as e:
            print(f"\n❌ Lỗi khi tạo file log mặc định: {e}")
    
    print(f"\n{Fore.GREEN}✅ HOÀN THÀNH SỬA LỖI ENCODING{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}💡 Bây giờ có thể chạy show_log.py hoặc run_with_progress.py{Style.RESET_ALL}")

def clean_all_logs():
    """Xóa tất cả file log và tạo mới"""
    print(f"\n{Fore.YELLOW}🗑️  XÓA TẤT CẢ FILE LOG{Style.RESET_ALL}")
    
    log_patterns = [
        "*.log",
        "registration.log",
        "output/*.log",
        "logs/*.log"
    ]
    
    import glob
    
    for pattern in log_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                print(f"✅ Đã xóa: {file}")
            except Exception as e:
                print(f"❌ Không thể xóa {file}: {e}")
    
    # Tạo file log mới
    try:
        with open("registration.log", 'w', encoding='utf-8') as f:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            f.write(f"# Clean log file created at {timestamp}\n")
            f.write(f"{timestamp} - INFO - All logs cleaned, ready to start\n")
        print(f"✅ Đã tạo file log mới: registration.log")
    except Exception as e:
        print(f"❌ Lỗi khi tạo file log mới: {e}")

def main():
    """Main function"""
    print("🔧 TOOL SỬA LỖI ENCODING LOG")
    print("="*50)
    print("1. Sửa lỗi encoding file log hiện có")
    print("2. Xóa tất cả log và tạo mới")
    print("3. Thoát")
    
    while True:
        try:
            choice = input(f"\n{Fore.GREEN}Chọn tùy chọn (1-3): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                fix_log_encoding()
                break
            elif choice == "2":
                confirm = input(f"{Fore.RED}Xác nhận xóa tất cả log? (y/n): {Style.RESET_ALL}").lower()
                if confirm == 'y':
                    clean_all_logs()
                break
            elif choice == "3":
                print("👋 Tạm biệt!")
                break
            else:
                print("❌ Lựa chọn không hợp lệ!")
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}👋 Tạm biệt!{Style.RESET_ALL}")
            break
        except Exception as e:
            print(f"❌ Lỗi: {e}")
    
    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
