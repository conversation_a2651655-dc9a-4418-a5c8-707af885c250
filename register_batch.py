"""
<PERSON><PERSON><PERSON> ký hàng loạt với username tuần tự
"""

import os
import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def generate_password():
    """Tạo password ngẫu nhiên"""
    import string
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(10)) + "123"

def claim_rewards(driver, user_id="391111507"):
    """Tự động nhận thưởng từ nhiệm vụ"""
    try:
        # URL trang nhiệm vụ
        task_url = f"https://www.13win16.com/home/<USER>"

        print(f"🎁 Đi đến trang nhiệm vụ...")
        driver.get(task_url)
        time.sleep(3)

        # Tìm tất cả nút "Nhận"
        claim_buttons = []

        # Tìm bằng text "Nhận"
        try:
            buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Nhận')]")
            claim_buttons.extend(buttons)
            print(f"✅ Tìm thấy {len(buttons)} nút 'Nhận'")
        except:
            pass

        if not claim_buttons:
            print("⚠️  Không tìm thấy nút 'Nhận'")
            return False

        # Click từng nút "Nhận"
        claimed_count = 0

        for i, button in enumerate(claim_buttons):
            try:
                if button.is_enabled() and button.is_displayed():
                    driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(1)
                    button.click()
                    claimed_count += 1
                    print(f"✅ Đã nhận thưởng {i+1}")
                    time.sleep(random.uniform(1, 2))
            except Exception as e:
                print(f"⚠️  Không thể nhận thưởng {i+1}: {e}")

        print(f"🎁 Đã nhận {claimed_count} phần thưởng")
        return claimed_count > 0

    except Exception as e:
        print(f"❌ Lỗi khi nhận thưởng: {e}")
        return False

def register_account(username, password, full_name):
    """Đăng ký một tài khoản và nhận thưởng"""
    print(f"\n🚀 ĐĂNG KÝ: {username}")
    print("="*50)

    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')

        # Sử dụng ChromeDriver local
        chromedriver_path = "drivers/chromedriver.exe"
        service = Service(chromedriver_path)

        # Tạo driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(5)

        # Điều hướng đến trang đăng ký
        url = "https://www.13win16.com/home/<USER>"
        driver.get(url)

        # Chờ trang load
        time.sleep(3)

        # Selector đã test thành công
        selectors = {
            'username': 'input[placeholder*="điện thoại"]',
            'password': 'input[placeholder*="Mật khẩu"]:not([placeholder*="xác nhận"])',
            'confirm_password': 'input[placeholder*="xác nhận"]',
            'real_name': 'input[placeholder="Họ Tên Thật"]',
            'submit_button': 'button'
        }

        # Điền form
        success_count = 0

        # 1. Username
        element = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, selectors['username']))
        )
        element.clear()
        element.send_keys(username)
        print(f"✅ Username: {username}")
        success_count += 1
        time.sleep(1)

        # 2. Password
        element = driver.find_element(By.CSS_SELECTOR, selectors['password'])
        element.clear()
        element.send_keys(password)
        print(f"✅ Password: {password}")
        success_count += 1
        time.sleep(1)

        # 3. Confirm Password
        element = driver.find_element(By.CSS_SELECTOR, selectors['confirm_password'])
        element.clear()
        element.send_keys(password)
        print("✅ Confirm Password")
        success_count += 1
        time.sleep(1)

        # 4. Họ tên thật
        element = driver.find_element(By.CSS_SELECTOR, selectors['real_name'])
        element.clear()
        element.send_keys(full_name)
        print(f"✅ Họ tên thật: {full_name}")
        success_count += 1
        time.sleep(1)

        # 5. Checkbox
        try:
            checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
            if not checkbox.is_selected():
                checkbox.click()
                print("✅ Checkbox")
        except:
            print("⚠️  Không tìm thấy checkbox")

        # 6. Submit
        submit_button = driver.find_element(By.CSS_SELECTOR, selectors['submit_button'])
        submit_button.click()
        print("🚀 Đã submit...")

        # Chờ kết quả
        time.sleep(5)

        # Kiểm tra kết quả
        page_source = driver.page_source.lower()

        if any(keyword in page_source for keyword in ["thành công", "success", "welcome"]):
            print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")

            # Lưu tài khoản thành công
            with open("successful_accounts.txt", "a", encoding="utf-8") as f:
                f.write(f"Username: {username}\n")
                f.write(f"Password: {password}\n")
                f.write(f"Full Name: {full_name}\n")
                f.write(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("-" * 50 + "\n")

            # Tự động nhận thưởng
            print(f"\n🎁 TỰ ĐỘNG NHẬN THƯỞNG...")
            time.sleep(3)  # Chờ hệ thống cập nhật

            if claim_rewards(driver):
                print(f"{Fore.GREEN}🎁 ĐÃ NHẬN THƯỞNG THÀNH CÔNG!{Style.RESET_ALL}")
            else:
                print(f"{Fore.YELLOW}⚠️  Không thể nhận thưởng{Style.RESET_ALL}")

            return True

        elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already"]):
            print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
            return False

        else:
            print(f"{Fore.RED}❓ Không xác định được kết quả{Style.RESET_ALL}")
            return False

    except Exception as e:
        print(f"{Fore.RED}❌ Lỗi: {e}{Style.RESET_ALL}")
        return False

    finally:
        if driver:
            try:
                driver.quit()
            except:
                pass

def register_batch():
    """Đăng ký hàng loạt"""
    print("🚀 ĐĂNG KÝ HÀNG LOẠT - 13WIN16.COM")
    print("="*60)

    # Cấu hình
    username_prefix = "taolatrumnohu"
    bank_name = "TRAN HOANG AN"

    # Nhập số lượng
    try:
        count = int(input("📝 Nhập số lượng tài khoản cần đăng ký: "))
        start_num = int(input("📝 Bắt đầu từ số (mặc định 1): ") or "1")
    except:
        print("❌ Số không hợp lệ!")
        return

    print(f"\n📋 Sẽ đăng ký {count} tài khoản:")
    print(f"👤 Username: {username_prefix}{start_num} → {username_prefix}{start_num + count - 1}")
    print(f"🏦 Họ tên thật: {bank_name}")
    print(f"🔒 Password: Ngẫu nhiên cho mỗi tài khoản")

    confirm = input(f"\n{Fore.YELLOW}Tiếp tục? (y/n): {Style.RESET_ALL}").lower()
    if confirm != 'y':
        print("❌ Đã hủy!")
        return

    # Bắt đầu đăng ký
    success_count = 0
    failed_count = 0

    for i in range(count):
        current_num = start_num + i
        username = f"{username_prefix}{current_num}"
        password = generate_password()

        print(f"\n📊 Tiến trình: {i+1}/{count}")

        try:
            if register_account(username, password, bank_name):
                success_count += 1
                print(f"{Fore.GREEN}✅ Thành công: {success_count}/{i+1}{Style.RESET_ALL}")
            else:
                failed_count += 1
                print(f"{Fore.RED}❌ Thất bại: {failed_count}/{i+1}{Style.RESET_ALL}")
        except Exception as e:
            failed_count += 1
            print(f"{Fore.RED}❌ Lỗi: {e}{Style.RESET_ALL}")

        # Delay giữa các lần đăng ký
        if i < count - 1:  # Không delay ở lần cuối
            delay = random.randint(3, 8)
            print(f"⏳ Chờ {delay}s trước lần tiếp theo...")
            time.sleep(delay)

    # Tóm tắt
    print(f"\n📊 KẾT QUẢ CUỐI CÙNG:")
    print(f"✅ Thành công: {success_count}")
    print(f"❌ Thất bại: {failed_count}")
    print(f"📁 Tài khoản thành công đã lưu trong: successful_accounts.txt")

def main():
    """Main function"""
    try:
        register_batch()
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}⚠️  Đã dừng bởi người dùng!{Style.RESET_ALL}")
    except Exception as e:
        print(f"❌ Lỗi: {e}")

    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
