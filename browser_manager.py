"""
<PERSON><PERSON><PERSON><PERSON> lý trình duyệt Chrome với proxy
"""

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager
from fake_useragent import UserAgent
import time
import random
import logging
import os
from config import BROWSER_CONFIG, REGISTRATION_CONFIG

class BrowserManager:
    def __init__(self, proxy=None):
        self.proxy = proxy
        self.driver = None
        self.logger = logging.getLogger(__name__)
        self.ua = UserAgent()

    def connect_to_existing_browser(self):
        """Kết nối với trình duyệt Chrome hiện có"""
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"127.0.0.1:{BROWSER_CONFIG['debug_port']}")

            # Tạo driver kết nối với browser hiện có
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Cấu hình timeout
            self.driver.set_page_load_timeout(BROWSER_CONFIG['page_load_timeout'])
            self.driver.implicitly_wait(BROWSER_CONFIG['implicit_wait'])

            self.logger.info("Đã kết nối với trình duyệt hiện có")
            return True

        except Exception as e:
            self.logger.error(f"Không thể kết nối với trình duyệt hiện có: {e}")
            return False

    def create_browser(self):
        """Tạo trình duyệt Chrome với cấu hình"""
        # Thử kết nối với browser hiện có trước
        if BROWSER_CONFIG.get('use_existing_browser', False):
            if self.connect_to_existing_browser():
                return True
            else:
                self.logger.info("Không thể kết nối browser hiện có, tạo browser mới...")

        try:
            chrome_options = Options()

            # Cấu hình cơ bản
            if BROWSER_CONFIG['headless']:
                chrome_options.add_argument('--headless')

            chrome_options.add_argument(f'--window-size={BROWSER_CONFIG["window_size"][0]},{BROWSER_CONFIG["window_size"][1]}')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')
            chrome_options.add_argument('--disable-plugins')

            if BROWSER_CONFIG['disable_images']:
                chrome_options.add_argument('--disable-images')

            # User Agent ngẫu nhiên
            if BROWSER_CONFIG['user_agent_rotation']:
                user_agent = self.ua.random
                chrome_options.add_argument(f'--user-agent={user_agent}')

            # Cấu hình proxy
            if self.proxy:
                chrome_options.add_argument(f'--proxy-server=http://{self.proxy}')
                self.logger.info(f"Sử dụng proxy: {self.proxy}")

            # Tắt thông báo
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2,
                    "geolocation": 2,
                    "media_stream": 2,
                }
            }
            chrome_options.add_experimental_option("prefs", prefs)

            # Tạo driver với xử lý lỗi
            try:
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as e:
                self.logger.error(f"Lỗi với ChromeDriverManager: {e}")
                # Thử sử dụng ChromeDriver local
                local_chromedriver = "drivers/chromedriver.exe"
                if os.path.exists(local_chromedriver):
                    self.logger.info("Thử sử dụng ChromeDriver local")
                    service = Service(local_chromedriver)
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                else:
                    raise e

            # Cấu hình timeout
            self.driver.set_page_load_timeout(BROWSER_CONFIG['page_load_timeout'])
            self.driver.implicitly_wait(BROWSER_CONFIG['implicit_wait'])

            self.logger.info("Đã tạo trình duyệt thành công")
            return True

        except Exception as e:
            self.logger.error(f"Lỗi khi tạo trình duyệt: {e}")
            return False

    def navigate_to_url(self, url):
        """Điều hướng đến URL"""
        try:
            self.driver.get(url)
            self.logger.info(f"Đã điều hướng đến: {url}")
            return True
        except TimeoutException:
            self.logger.error(f"Timeout khi load trang: {url}")
            return False
        except Exception as e:
            self.logger.error(f"Lỗi khi điều hướng: {e}")
            return False

    def wait_for_element(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """Chờ element xuất hiện"""
        try:
            wait = WebDriverWait(self.driver, timeout)
            element = wait.until(EC.presence_of_element_located((by, selector)))
            return element
        except TimeoutException:
            self.logger.warning(f"Không tìm thấy element: {selector}")
            return None

    def wait_for_clickable(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """Chờ element có thể click"""
        try:
            wait = WebDriverWait(self.driver, timeout)
            element = wait.until(EC.element_to_be_clickable((by, selector)))
            return element
        except TimeoutException:
            self.logger.warning(f"Element không thể click: {selector}")
            return None

    def safe_send_keys(self, element, text, clear_first=True):
        """Nhập text an toàn với delay"""
        try:
            if clear_first:
                element.clear()

            # Nhập từng ký tự với delay ngẫu nhiên
            for char in text:
                element.send_keys(char)
                time.sleep(random.uniform(0.05, 0.15))

            # Delay sau khi nhập xong
            self.random_delay()
            return True
        except Exception as e:
            self.logger.error(f"Lỗi khi nhập text: {e}")
            return False

    def safe_click(self, element):
        """Click an toàn với delay"""
        try:
            # Scroll đến element
            self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
            time.sleep(0.5)

            # Click
            element.click()
            self.random_delay()
            return True
        except Exception as e:
            self.logger.error(f"Lỗi khi click: {e}")
            return False

    def random_delay(self, min_delay=None, max_delay=None):
        """Delay ngẫu nhiên"""
        if min_delay is None or max_delay is None:
            min_delay, max_delay = REGISTRATION_CONFIG['delay_between_actions']

        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)

    def take_screenshot(self, filename):
        """Chụp màn hình"""
        try:
            self.driver.save_screenshot(filename)
            self.logger.info(f"Đã chụp màn hình: {filename}")
            return True
        except Exception as e:
            self.logger.error(f"Lỗi khi chụp màn hình: {e}")
            return False

    def get_page_source(self):
        """Lấy source code trang"""
        try:
            return self.driver.page_source
        except Exception as e:
            self.logger.error(f"Lỗi khi lấy page source: {e}")
            return None

    def execute_script(self, script):
        """Thực thi JavaScript"""
        try:
            return self.driver.execute_script(script)
        except Exception as e:
            self.logger.error(f"Lỗi khi thực thi script: {e}")
            return None

    def close_browser(self):
        """Đóng trình duyệt"""
        try:
            if self.driver:
                self.driver.quit()
                self.logger.info("Đã đóng trình duyệt")
        except Exception as e:
            self.logger.error(f"Lỗi khi đóng trình duyệt: {e}")

    def __enter__(self):
        """Context manager enter"""
        self.create_browser()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close_browser()
        return False
