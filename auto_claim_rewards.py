"""
Tự động nhận thưởng sau khi đăng ký
"""

import os
import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def claim_rewards(driver, user_id="391111507"):
    """Tự động nhận thưởng từ nhiệm vụ"""
    print(f"\n🎁 TỰ ĐỘNG NHẬN THƯỞNG")
    print("="*50)
    
    try:
        # URL trang nhiệm vụ
        task_url = f"https://www.13win16.com/home/<USER>"
        
        print(f"🌐 Đi đến trang nhiệm vụ...")
        driver.get(task_url)
        time.sleep(3)
        
        print(f"📄 URL: {task_url}")
        
        # Tìm tất cả nút "Nhận" (màu xanh lá)
        claim_buttons = []
        
        # Các selector có thể cho nút "Nhận"
        selectors = [
            'button:contains("Nhận")',
            'button[style*="background-color: rgb(34, 197, 94)"]',  # Màu xanh lá
            'button[style*="background: rgb(34, 197, 94)"]',
            '.bg-green-500',
            '.btn-success',
            'button[class*="green"]',
            'button[class*="success"]'
        ]
        
        print("🔍 Tìm các nút 'Nhận'...")
        
        # Tìm bằng text "Nhận"
        try:
            buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Nhận')]")
            claim_buttons.extend(buttons)
            print(f"✅ Tìm thấy {len(buttons)} nút có text 'Nhận'")
        except:
            pass
        
        # Tìm bằng CSS selector
        for selector in selectors:
            try:
                buttons = driver.find_elements(By.CSS_SELECTOR, selector)
                for btn in buttons:
                    if btn not in claim_buttons:
                        claim_buttons.append(btn)
            except:
                pass
        
        print(f"📊 Tổng cộng tìm thấy: {len(claim_buttons)} nút có thể nhận")
        
        if not claim_buttons:
            print("⚠️  Không tìm thấy nút 'Nhận' nào")
            return False
        
        # Click từng nút "Nhận"
        claimed_count = 0
        
        for i, button in enumerate(claim_buttons):
            try:
                # Kiểm tra nút có thể click không
                if button.is_enabled() and button.is_displayed():
                    # Scroll đến nút
                    driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(1)
                    
                    # Click nút
                    button.click()
                    claimed_count += 1
                    
                    print(f"✅ Đã click nút {i+1}: Nhận thưởng")
                    
                    # Chờ một chút giữa các lần click
                    time.sleep(random.uniform(1, 3))
                    
                else:
                    print(f"⚠️  Nút {i+1} không thể click (đã nhận hoặc chưa đủ điều kiện)")
                    
            except Exception as e:
                print(f"❌ Lỗi khi click nút {i+1}: {e}")
        
        print(f"\n📊 Kết quả nhận thưởng:")
        print(f"✅ Đã nhận: {claimed_count} phần thưởng")
        
        # Chụp screenshot sau khi nhận thưởng
        try:
            driver.save_screenshot("after_claim_rewards.png")
            print("📸 Đã chụp screenshot: after_claim_rewards.png")
        except:
            pass
        
        return claimed_count > 0
        
    except Exception as e:
        print(f"❌ Lỗi khi nhận thưởng: {e}")
        return False

def register_and_claim(username, password, full_name):
    """Đăng ký tài khoản và tự động nhận thưởng"""
    print(f"\n🚀 ĐĂNG KÝ VÀ NHẬN THƯỞNG: {username}")
    print("="*60)
    
    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')
        
        # Sử dụng ChromeDriver local
        chromedriver_path = "drivers/chromedriver.exe"
        service = Service(chromedriver_path)
        
        # Tạo driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(5)
        
        # BƯỚC 1: Đăng ký tài khoản
        print("📝 BƯỚC 1: Đăng ký tài khoản")
        
        # Điều hướng đến trang đăng ký
        register_url = "https://www.13win16.com/home/<USER>"
        driver.get(register_url)
        time.sleep(3)
        
        # Selector form đăng ký
        selectors = {
            'username': 'input[placeholder*="điện thoại"]',
            'password': 'input[placeholder*="Mật khẩu"]:not([placeholder*="xác nhận"])',
            'confirm_password': 'input[placeholder*="xác nhận"]',
            'real_name': 'input[placeholder="Họ Tên Thật"]',
            'submit_button': 'button'
        }
        
        # Điền form đăng ký
        element = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, selectors['username']))
        )
        element.clear()
        element.send_keys(username)
        print(f"✅ Username: {username}")
        time.sleep(1)
        
        element = driver.find_element(By.CSS_SELECTOR, selectors['password'])
        element.clear()
        element.send_keys(password)
        print(f"✅ Password: {password}")
        time.sleep(1)
        
        element = driver.find_element(By.CSS_SELECTOR, selectors['confirm_password'])
        element.clear()
        element.send_keys(password)
        print("✅ Confirm Password")
        time.sleep(1)
        
        element = driver.find_element(By.CSS_SELECTOR, selectors['real_name'])
        element.clear()
        element.send_keys(full_name)
        print(f"✅ Họ tên thật: {full_name}")
        time.sleep(1)
        
        # Checkbox
        try:
            checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
            if not checkbox.is_selected():
                checkbox.click()
                print("✅ Checkbox")
        except:
            print("⚠️  Không tìm thấy checkbox")
        
        # Submit đăng ký
        submit_button = driver.find_element(By.CSS_SELECTOR, selectors['submit_button'])
        submit_button.click()
        print("🚀 Đã submit đăng ký...")
        
        # Chờ kết quả đăng ký
        time.sleep(5)
        
        # Kiểm tra đăng ký thành công
        page_source = driver.page_source.lower()
        
        if any(keyword in page_source for keyword in ["thành công", "success", "welcome"]):
            print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
            
            # Lưu tài khoản
            with open("successful_accounts.txt", "a", encoding="utf-8") as f:
                f.write(f"Username: {username}\n")
                f.write(f"Password: {password}\n")
                f.write(f"Full Name: {full_name}\n")
                f.write(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("-" * 50 + "\n")
            
            # BƯỚC 2: Tự động nhận thưởng
            print(f"\n📝 BƯỚC 2: Tự động nhận thưởng")
            time.sleep(3)  # Chờ hệ thống cập nhật
            
            if claim_rewards(driver):
                print(f"{Fore.GREEN}🎁 ĐÃ NHẬN THƯỞNG THÀNH CÔNG!{Style.RESET_ALL}")
                return True
            else:
                print(f"{Fore.YELLOW}⚠️  Không thể nhận thưởng (có thể chưa có hoặc đã nhận){Style.RESET_ALL}")
                return True  # Vẫn coi là thành công vì đăng ký OK
            
        elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already"]):
            print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
            return False
            
        else:
            print(f"{Fore.RED}❓ Không xác định được kết quả đăng ký{Style.RESET_ALL}")
            return False
        
    except Exception as e:
        print(f"{Fore.RED}❌ Lỗi: {e}{Style.RESET_ALL}")
        return False
        
    finally:
        if driver:
            try:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                driver.quit()
            except:
                pass

def main():
    """Main function"""
    try:
        print("🎁 TOOL ĐĂNG KÝ VÀ NHẬN THƯỞNG TỰ ĐỘNG")
        print("="*60)
        print("Tool này sẽ:")
        print("1. Đăng ký tài khoản trên 13win16.com")
        print("2. Tự động đi đến trang nhiệm vụ")
        print("3. Click tất cả nút 'Nhận' để nhận thưởng")
        print("4. Lưu thông tin tài khoản thành công")
        
        # Test với 1 tài khoản
        username = "taolatrumnohu2"  # Tài khoản tiếp theo
        password = "Test123456"
        full_name = "TRAN HOANG AN"
        
        print(f"\n📝 Test với tài khoản: {username}")
        print(f"🔒 Password: {password}")
        print(f"👤 Họ tên thật: {full_name}")
        
        confirm = input(f"\n{Fore.YELLOW}Tiếp tục? (y/n): {Style.RESET_ALL}").lower()
        
        if confirm == 'y':
            success = register_and_claim(username, password, full_name)
            
            if success:
                print(f"\n{Fore.GREEN}🎉 HOÀN THÀNH!{Style.RESET_ALL}")
                print("✅ Đăng ký thành công")
                print("🎁 Đã nhận thưởng")
                print("💾 Đã lưu thông tin tài khoản")
            else:
                print(f"\n{Fore.RED}❌ THẤT BẠI!{Style.RESET_ALL}")
        else:
            print("❌ Đã hủy!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
