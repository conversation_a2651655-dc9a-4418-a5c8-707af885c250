"""
Đ<PERSON><PERSON> ký trực tiếp với selector đã biết
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def register_account():
    """Đăng ký tài khoản trực tiếp"""
    print("🚀 ĐĂNG KÝ TÀI KHOẢN TRỰC TIẾP")
    print("="*50)

    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')

        # Sử dụng ChromeDriver local
        chromedriver_path = "drivers/chromedriver.exe"
        if os.path.exists(chromedriver_path):
            print(f"✅ Sử dụng ChromeDriver local: {chromedriver_path}")
            service = Service(chromedriver_path)
        else:
            print("❌ Không tìm thấy ChromeDriver local")
            return False

        # Tạo driver
        print("🚀 Đang tạo browser...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(5)

        print("✅ Browser đã tạo thành công")

        # Điều hướng đến trang đăng ký
        url = "https://www.13win16.com/home/<USER>"
        print(f"🌐 Đang điều hướng đến: {url}")

        driver.get(url)
        print("✅ Đã điều hướng thành công")

        # Chờ trang load
        print("⏳ Chờ trang load...")
        time.sleep(3)

        # Thông tin tài khoản
        account = {
            'username': 'taolatrumnohu1',
            'password': 'Test123456',
            'full_name': 'TRAN HOANG AN'  # Tên tài khoản ngân hàng
        }

        print(f"📝 Đăng ký tài khoản: {account['username']}")

        # Selector đã test thành công
        selectors = {
            'username': 'input[placeholder*="điện thoại"]',
            'password': 'input[placeholder*="Mật khẩu"]:not([placeholder*="xác nhận"])',
            'confirm_password': 'input[placeholder*="xác nhận"]',
            'real_name': 'input[placeholder="Họ Tên Thật"]',
            'submit_button': 'button'
        }

        # Điền form
        success_count = 0

        # 1. Username
        try:
            print("📝 Điền username...")
            element = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, selectors['username']))
            )
            element.clear()
            element.send_keys(account['username'])
            print(f"✅ Đã điền username: {account['username']}")
            success_count += 1
            time.sleep(1)
        except Exception as e:
            print(f"❌ Lỗi khi điền username: {e}")
            return False

        # 2. Password
        try:
            print("🔒 Điền password...")
            element = driver.find_element(By.CSS_SELECTOR, selectors['password'])
            element.clear()
            element.send_keys(account['password'])
            print("✅ Đã điền password")
            success_count += 1
            time.sleep(1)
        except Exception as e:
            print(f"❌ Lỗi khi điền password: {e}")
            return False

        # 3. Confirm Password
        try:
            print("🔒 Điền confirm password...")
            element = driver.find_element(By.CSS_SELECTOR, selectors['confirm_password'])
            element.clear()
            element.send_keys(account['password'])
            print("✅ Đã điền confirm password")
            success_count += 1
            time.sleep(1)
        except Exception as e:
            print(f"❌ Lỗi khi điền confirm password: {e}")
            return False

        # 4. Họ tên thật (tên tài khoản ngân hàng)
        try:
            print("👤 Điền họ tên thật (tên tài khoản ngân hàng)...")
            element = driver.find_element(By.CSS_SELECTOR, selectors['real_name'])
            element.clear()
            element.send_keys(account['full_name'])
            print(f"✅ Đã điền họ tên thật: {account['full_name']}")
            success_count += 1
            time.sleep(1)
        except Exception as e:
            print(f"⚠️  Không tìm thấy trường họ tên thật: {e}")

        # 5. Checkbox (nếu có)
        try:
            print("☑️  Tìm và click checkbox...")
            checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
            if not checkbox.is_selected():
                checkbox.click()
                print("✅ Đã click checkbox")
                time.sleep(1)
        except Exception as e:
            print(f"⚠️  Không tìm thấy checkbox: {e}")

        # Chụp screenshot trước khi submit
        try:
            driver.save_screenshot("before_real_submit.png")
            print("📸 Đã chụp screenshot: before_real_submit.png")
        except:
            pass

        # 5. Submit
        print("\n🎯 Chuẩn bị submit...")

        # Hỏi user có muốn submit thật không
        confirm = input(f"{Fore.YELLOW}⚠️  Bạn có muốn THỰC SỰ ĐĂNG KÝ tài khoản '{account['username']}' không? (y/n): {Style.RESET_ALL}").lower()

        if confirm == 'y':
            try:
                print("🚀 Đang submit...")
                submit_button = driver.find_element(By.CSS_SELECTOR, selectors['submit_button'])
                submit_button.click()

                print("✅ Đã click submit!")
                print("⏳ Chờ kết quả...")
                time.sleep(5)

                # Chụp screenshot sau submit
                try:
                    driver.save_screenshot("after_real_submit.png")
                    print("📸 Đã chụp screenshot: after_real_submit.png")
                except:
                    pass

                # Kiểm tra kết quả
                page_source = driver.page_source.lower()

                if any(keyword in page_source for keyword in ["thành công", "success", "welcome", "đăng ký thành công"]):
                    print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")

                    # Lưu tài khoản thành công
                    with open("successful_accounts.txt", "a", encoding="utf-8") as f:
                        f.write(f"Username: {account['username']}\n")
                        f.write(f"Password: {account['password']}\n")
                        f.write(f"Full Name: {account['full_name']}\n")
                        f.write(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write("-" * 50 + "\n")

                    print(f"💾 Đã lưu vào: successful_accounts.txt")
                    return True

                elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already", "trùng"]):
                    print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                    return False

                else:
                    print(f"{Fore.RED}❓ Không xác định được kết quả{Style.RESET_ALL}")
                    print("Kiểm tra screenshot để xem kết quả")
                    return False

            except Exception as e:
                print(f"❌ Lỗi khi submit: {e}")
                return False
        else:
            print("❌ Đã hủy đăng ký")
            return False

    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

    finally:
        if driver:
            try:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                driver.quit()
                print("🔒 Đã đóng browser")
            except:
                pass

def main():
    """Main function"""
    try:
        print("🎯 TOOL ĐĂNG KÝ TRỰC TIẾP - 13WIN16.COM")
        print("="*60)
        print("⚠️  Tool này sẽ THỰC SỰ đăng ký tài khoản!")
        print("📝 Username sẽ là: taolatrumnohu1")
        print("🔒 Password sẽ là: Test123456")
        print("👤 Họ tên thật sẽ là: TRAN HOANG AN (tên tài khoản ngân hàng)")

        confirm = input(f"\n{Fore.YELLOW}Tiếp tục? (y/n): {Style.RESET_ALL}").lower()

        if confirm == 'y':
            success = register_account()

            if success:
                print(f"\n{Fore.GREEN}🎉 HOÀN THÀNH!{Style.RESET_ALL}")
                print("Tài khoản đã được đăng ký thành công!")
            else:
                print(f"\n{Fore.RED}❌ ĐĂNG KÝ THẤT BẠI!{Style.RESET_ALL}")
        else:
            print("❌ Đã hủy!")

    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
