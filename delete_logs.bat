@echo off
chcp 65001 >nul
title Xóa File Log

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        XÓA FILE LOG                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🧹 Đang xóa tất cả file log...

REM Xóa file log chính
if exist "registration.log" (
    del "registration.log"
    echo ✅ Đã xóa: registration.log
) else (
    echo ℹ️  Không tìm thấy: registration.log
)

REM Xóa file log trong thư mục output
if exist "output\registration.log" (
    del "output\registration.log"
    echo ✅ Đã xóa: output\registration.log
) else (
    echo ℹ️  Không tìm thấy: output\registration.log
)

REM Xóa file log trong thư mục logs
if exist "logs\registration.log" (
    del "logs\registration.log"
    echo ✅ Đã xóa: logs\registration.log
) else (
    echo ℹ️  Không tìm thấy: logs\registration.log
)

REM Xóa các file log khác
for %%f in (*.log) do (
    del "%%f"
    echo ✅ Đã xóa: %%f
)

echo.
echo ✅ Hoàn thành xóa file log!
echo.
echo 📋 Bước tiếp theo:
echo    1. Chạ<PERSON>: python clean_start.py
echo    2. Hoặc: python main.py
echo.

pause
