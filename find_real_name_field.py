"""
Tìm trường họ tên thật trong form đăng ký
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def find_real_name_field():
    """Tìm trường họ tên thật"""
    print("🔍 TÌM TRƯỜNG HỌ TÊN THẬT")
    print("="*50)
    
    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')
        
        # Sử dụng ChromeDriver local
        chromedriver_path = "drivers/chromedriver.exe"
        if os.path.exists(chromedriver_path):
            print(f"✅ Sử dụng ChromeDriver local: {chromedriver_path}")
            service = Service(chromedriver_path)
        else:
            print("❌ Không tìm thấy ChromeDriver local")
            return False
        
        # Tạo driver
        print("🚀 Đang tạo browser...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(5)
        
        print("✅ Browser đã tạo thành công")
        
        # Điều hướng đến trang đăng ký
        url = "https://www.13win16.com/home/<USER>"
        print(f"🌐 Đang điều hướng đến: {url}")
        
        driver.get(url)
        print("✅ Đã điều hướng thành công")
        
        # Chờ trang load
        print("⏳ Chờ trang load...")
        time.sleep(5)
        
        # Tìm tất cả input text
        print("\n🔍 Tìm tất cả input text...")
        text_inputs = driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
        
        print(f"📝 Tìm thấy {len(text_inputs)} input text:")
        
        for i, input_elem in enumerate(text_inputs):
            try:
                name = input_elem.get_attribute('name') or 'N/A'
                id_attr = input_elem.get_attribute('id') or 'N/A'
                placeholder = input_elem.get_attribute('placeholder') or 'N/A'
                class_attr = input_elem.get_attribute('class') or 'N/A'
                
                print(f"  [{i+1}] Name: {name}")
                print(f"      ID: {id_attr}")
                print(f"      Placeholder: {placeholder}")
                print(f"      Class: {class_attr}")
                print(f"      Selector: input[type='text']:nth-of-type({i+1})")
                print("-" * 40)
                
                # Kiểm tra có phải trường họ tên không
                if any(keyword in placeholder.lower() for keyword in ['họ', 'tên', 'name', 'real', 'full']):
                    print(f"🎯 CÓ THỂ LÀ TRƯỜNG HỌ TÊN: {placeholder}")
                
            except Exception as e:
                print(f"  [{i+1}] Lỗi khi lấy thông tin: {e}")
        
        # Test điền vào input thứ 4 (dự đoán là họ tên)
        if len(text_inputs) >= 4:
            print(f"\n🧪 Test điền vào input thứ 4...")
            try:
                fourth_input = text_inputs[3]  # Index 3 = input thứ 4
                placeholder = fourth_input.get_attribute('placeholder') or 'N/A'
                print(f"📝 Placeholder của input thứ 4: {placeholder}")
                
                # Test điền
                fourth_input.clear()
                fourth_input.send_keys("TRAN HOANG AN")
                print("✅ Đã test điền 'TRAN HOANG AN' vào input thứ 4")
                
                # Chụp screenshot
                driver.save_screenshot("test_real_name_field.png")
                print("📸 Đã chụp screenshot: test_real_name_field.png")
                
            except Exception as e:
                print(f"❌ Lỗi khi test input thứ 4: {e}")
        
        # Tìm input có placeholder chứa từ khóa họ tên
        print(f"\n🔍 Tìm input có placeholder chứa từ khóa họ tên...")
        name_keywords = ['họ', 'tên', 'name', 'real', 'full', 'thật']
        
        for keyword in name_keywords:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, f'input[placeholder*="{keyword}"]')
                if elements:
                    print(f"✅ Tìm thấy {len(elements)} input với keyword '{keyword}':")
                    for i, elem in enumerate(elements):
                        placeholder = elem.get_attribute('placeholder') or 'N/A'
                        print(f"  [{i+1}] Placeholder: {placeholder}")
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
        
    finally:
        if driver:
            try:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                driver.quit()
                print("🔒 Đã đóng browser")
            except:
                pass

def main():
    """Main function"""
    try:
        print("🔍 TÌM TRƯỜNG HỌ TÊN THẬT TRONG FORM 13WIN16.COM")
        print("="*60)
        print("Tool này sẽ:")
        print("1. Mở trang đăng ký 13win16.com")
        print("2. Tìm tất cả input text")
        print("3. Phân tích placeholder và thuộc tính")
        print("4. Test điền 'TRAN HOANG AN' vào input thứ 4")
        print("5. Chụp screenshot để kiểm tra")
        
        confirm = input(f"\n{Fore.YELLOW}Tiếp tục? (y/n): {Style.RESET_ALL}").lower()
        
        if confirm == 'y':
            success = find_real_name_field()
            
            if success:
                print(f"\n{Fore.GREEN}🎉 HOÀN THÀNH!{Style.RESET_ALL}")
                print("Kiểm tra:")
                print("- Console output để xem thông tin các input")
                print("- File test_real_name_field.png để xem kết quả")
            else:
                print(f"\n{Fore.RED}❌ THẤT BẠI!{Style.RESET_ALL}")
        else:
            print("❌ Đã hủy!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
