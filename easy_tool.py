"""
TOOL ĐĂNG KÝ DỄ DÀNG - KHÔNG LỖI
Mỗi tài khoản = 1 Chrome riêng, không tạo profile phức tạp
"""

import time
import random
import string
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class EasyTool:
    def __init__(self):
        self.chromedriver_path = "drivers/chromedriver.exe"
        self.screenshot_folder = "screenshots"
        self.active_drivers = {}
        
        # Tạo folder screenshot đơn giản
        if not os.path.exists(self.screenshot_folder):
            os.makedirs(self.screenshot_folder)
            print(f"📁 Đã tạo folder: {self.screenshot_folder}")
    
    def create_chrome(self, username):
        """Tạo Chrome không profile để tránh lỗi"""
        try:
            chrome_options = Options()
            
            # Chỉ các option tối thiểu
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--log-level=3')
            
            # KHÔNG dùng user-data-dir để tránh lỗi
            # Mỗi Chrome sẽ dùng profile tạm
            
            service = Service(self.chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(3)
            
            self.active_drivers[username] = driver
            print(f"✅ Chrome cho {username}")
            return driver
            
        except Exception as e:
            print(f"❌ Lỗi tạo Chrome: {e}")
            return None
    
    def screenshot(self, filename, driver):
        """Chụp screenshot"""
        try:
            filepath = os.path.join(self.screenshot_folder, filename)
            driver.save_screenshot(filepath)
            print(f"📸 {filename}")
        except:
            pass
    
    def close_popups(self, driver):
        """Đóng popup"""
        try:
            print("🔔 Đóng popup...")
            time.sleep(3)
            
            # Thử 3 lần đóng popup
            for attempt in range(3):
                closed = False
                
                # Thử click OK
                try:
                    ok_btn = driver.find_element(By.XPATH, "//button[contains(text(), 'OK')]")
                    if ok_btn.is_displayed():
                        ok_btn.click()
                        print("✅ Đóng OK")
                        closed = True
                        time.sleep(1)
                except:
                    pass
                
                # Thử click Đóng
                if not closed:
                    try:
                        close_btn = driver.find_element(By.XPATH, "//button[contains(text(), 'Đóng')]")
                        if close_btn.is_displayed():
                            close_btn.click()
                            print("✅ Đóng popup")
                            closed = True
                            time.sleep(1)
                    except:
                        pass
                
                # Thử ESC
                if not closed:
                    try:
                        driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                        print("✅ ESC")
                        time.sleep(1)
                    except:
                        pass
                
                if not closed:
                    break
            
            return True
        except:
            return False
    
    def register_account(self, username, password, fullname):
        """Đăng ký tài khoản"""
        try:
            print(f"\n🚀 ĐĂNG KÝ: {username}")
            
            # Tạo Chrome
            driver = self.create_chrome(username)
            if not driver:
                return False
            
            # Đi đến trang đăng ký
            driver.get("https://www.13win16.com/home/<USER>")
            time.sleep(8)
            
            self.screenshot(f"01_{username}_register.png", driver)
            
            # Tìm input
            inputs = driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
            print(f"📝 Tìm thấy {len(inputs)} input")
            
            if len(inputs) < 3:
                print("❌ Không đủ input")
                return False
            
            # Điền form
            try:
                # Username
                inputs[0].clear()
                inputs[0].send_keys(username)
                print(f"✅ Username: {username}")
                time.sleep(1)
                
                # Password
                inputs[1].clear()
                inputs[1].send_keys(password)
                print(f"✅ Password")
                time.sleep(1)
                
                # Confirm Password
                if len(inputs) >= 3:
                    inputs[2].clear()
                    inputs[2].send_keys(password)
                    print("✅ Confirm Password")
                    time.sleep(1)
                
                # Fullname
                if len(inputs) >= 4:
                    inputs[3].clear()
                    inputs[3].send_keys(fullname)
                    print(f"✅ Fullname: {fullname}")
                    time.sleep(1)
                    
            except Exception as e:
                print(f"❌ Lỗi điền form: {e}")
                return False
            
            self.screenshot(f"02_{username}_filled.png", driver)
            
            # Checkbox
            try:
                checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Checkbox")
            except:
                print("⚠️  Không có checkbox")
            
            # Submit
            try:
                buttons = driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    submit_btn = buttons[-1]
                    driver.execute_script("arguments[0].scrollIntoView(true);", submit_btn)
                    time.sleep(1)
                    submit_btn.click()
                    print("🚀 Submit")
                    time.sleep(8)
                else:
                    print("❌ Không tìm thấy button")
                    return False
            except Exception as e:
                print(f"❌ Lỗi submit: {e}")
                return False
            
            self.screenshot(f"03_{username}_result.png", driver)
            
            # Kiểm tra kết quả
            page_source = driver.page_source.lower()
            
            if any(keyword in page_source for keyword in ["thành công", "success", "welcome"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
                
                # Đóng popup
                self.close_popups(driver)
                self.screenshot(f"04_{username}_after_popup.png", driver)
                
                # Chuyển trang thưởng
                try:
                    driver.get("https://www.13win16.com/home/<USER>")
                    time.sleep(3)
                    self.screenshot(f"05_{username}_reward_page.png", driver)
                    print("✅ Đã chuyển trang thưởng")
                except:
                    print("⚠️  Không chuyển được trang thưởng")
                
                # Lưu tài khoản
                with open("accounts.txt", "a", encoding="utf-8") as f:
                    f.write(f"{username}|{password}|{fullname}\n")
                
                print(f"💰 Tài khoản có ~22D, cần app để đạt 52D")
                return True
                
            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already"]):
                print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                return False
            else:
                print(f"{Fore.RED}❓ Không rõ kết quả{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi đăng ký: {e}")
            return False
    
    def generate_password(self):
        """Tạo password ngẫu nhiên"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(8)) + "123"
    
    def run_registration(self):
        """Chạy đăng ký"""
        print("🎯 TOOL ĐĂNG KÝ DỄ DÀNG")
        print("="*40)
        
        # Nhập thông tin
        prefix = input("👤 Prefix username: ").strip() or "taolatrumnohu"
        try:
            count = int(input("📝 Số lượng tài khoản: "))
            start = int(input("📝 Bắt đầu từ số: ") or "1")
        except:
            print("❌ Số không hợp lệ!")
            return
        
        fullname = "TRAN HOANG AN"
        
        print(f"\n📋 Sẽ đăng ký {count} tài khoản:")
        print(f"👤 {prefix}{start} → {prefix}{start+count-1}")
        print(f"👨 Họ tên: {fullname}")
        print(f"🌐 Mỗi tài khoản = 1 Chrome riêng")
        
        if input("\nTiếp tục? (y/n): ").lower() != 'y':
            return
        
        success_count = 0
        failed_count = 0
        
        try:
            for i in range(count):
                num = start + i
                username = f"{prefix}{num}"
                password = self.generate_password()
                
                print(f"\n📊 {i+1}/{count}: {username}")
                
                if self.register_account(username, password, fullname):
                    success_count += 1
                    print(f"{Fore.GREEN}✅ Thành công: {success_count}{Style.RESET_ALL}")
                else:
                    failed_count += 1
                    print(f"{Fore.RED}❌ Thất bại: {failed_count}{Style.RESET_ALL}")
                
                # Delay giữa các lần đăng ký
                if i < count - 1:
                    delay = random.randint(3, 8)
                    print(f"⏳ Chờ {delay}s...")
                    time.sleep(delay)
            
            # Kết quả cuối
            print(f"\n📊 KẾT QUẢ CUỐI:")
            print(f"✅ Đăng ký thành công: {success_count}")
            print(f"❌ Đăng ký thất bại: {failed_count}")
            print(f"🌐 Chrome đang chạy: {len(self.active_drivers)}")
            print(f"📁 Tài khoản lưu trong: accounts.txt")
            print(f"📸 Screenshot trong: {self.screenshot_folder}/")
            
            if success_count > 0:
                print(f"\n💰 TÌNH TRẠNG THƯỞNG:")
                print(f"📊 Mỗi tài khoản có ~22D từ web")
                print(f"🎯 Cần đạt 52D/tài khoản")
                print(f"📱 Thiếu ~30D → Cần đăng nhập app (+35D)")
                
                print(f"\n{Fore.YELLOW}📱 HƯỚNG DẪN NHẬN THÊM THƯỞNG:{Style.RESET_ALL}")
                print("1. Tải app 13win trên điện thoại/BlueStacks")
                print("2. Đăng nhập từng tài khoản trong app")
                print("3. Nhận thưởng app (+35D)")
                print("4. Tổng: 22D + 35D = 57D ✅")
                
                print(f"\n{Fore.GREEN}🎉 KHI TẤT CẢ TÀI KHOẢN ĐẠT 52D:{Style.RESET_ALL}")
                print("→ Có thể đóng tất cả Chrome")
                print("→ Mỗi tài khoản sẽ có 52+ D")
            
        except KeyboardInterrupt:
            print(f"\n⚠️  Đã dừng bởi người dùng!")
        
        finally:
            # Hỏi đóng Chrome
            if self.active_drivers:
                print(f"\n🌐 Hiện có {len(self.active_drivers)} Chrome đang chạy")
                choice = input(f"{Fore.CYAN}Đóng tất cả Chrome? (y/n): {Style.RESET_ALL}").lower()
                
                if choice == 'y':
                    self.close_all_chrome()
                else:
                    print("🌐 Chrome vẫn mở để bạn kiểm tra thủ công")
                    print("💡 Mỗi Chrome đang ở trang thưởng, sẵn sàng nhận thưởng")
    
    def close_all_chrome(self):
        """Đóng tất cả Chrome"""
        for username, driver in self.active_drivers.items():
            try:
                driver.quit()
                print(f"✅ Đã đóng Chrome: {username}")
            except:
                pass
        self.active_drivers.clear()
        print("🔒 Đã đóng tất cả Chrome")

def main():
    """Main function"""
    try:
        tool = EasyTool()
        tool.run_registration()
    except Exception as e:
        print(f"❌ Lỗi chính: {e}")

if __name__ == "__main__":
    main()
