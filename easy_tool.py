"""
TOOL ĐĂNG KÝ DỄ DÀNG - KHÔNG LỖI
Mỗi tài khoản = 1 Chrome riêng, không tạo profile phức tạp
"""

import time
import random
import string
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class EasyTool:
    def __init__(self):
        self.chromedriver_path = "drivers/chromedriver.exe"
        self.screenshot_folder = "screenshots"
        self.active_drivers = {}

        # Tạo folder screenshot đơn giản
        if not os.path.exists(self.screenshot_folder):
            os.makedirs(self.screenshot_folder)
            print(f"📁 Đã tạo folder: {self.screenshot_folder}")

    def create_chrome(self, username):
        """Tạo Chrome không profile để tránh lỗi"""
        try:
            chrome_options = Options()

            # Chỉ các option tối thiểu
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--log-level=3')

            # KHÔNG dùng user-data-dir để tránh lỗi
            # Mỗi Chrome sẽ dùng profile tạm

            service = Service(self.chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(3)

            self.active_drivers[username] = driver
            print(f"✅ Chrome cho {username}")
            return driver

        except Exception as e:
            print(f"❌ Lỗi tạo Chrome: {e}")
            return None

    def screenshot(self, filename, driver):
        """Chụp screenshot"""
        try:
            filepath = os.path.join(self.screenshot_folder, filename)
            driver.save_screenshot(filepath)
            print(f"📸 {filename}")
        except:
            pass

    def handle_red_envelope_and_tasks(self, driver, username):
        """Xử lý hồng bao và nhiệm vụ"""
        try:
            print("🧧 Xử lý hồng bao và nhiệm vụ...")
            time.sleep(3)

            # Bước 1: Đóng popup bằng dấu X
            print("🔔 Đóng popup bằng dấu X...")
            self.close_popup_with_x(driver)

            # Bước 2: Điều hướng đến trang task chính xác
            print("📋 Điều hướng đến trang nhiệm vụ...")
            try:
                task_url = "https://www.13win16.com/home/<USER>"
                driver.get(task_url)
                time.sleep(5)  # Chờ lâu hơn để trang load
                print(f"✅ Đã điều hướng đến: {task_url}")
            except:
                print("⚠️  Không thể điều hướng đến trang task")

            self.screenshot(f"04_{username}_task_page.png", driver)

            # Bước 3: Mở hồng bao
            print("🧧 Mở hồng bao...")
            mo_button_found = False

            mo_selectors = [
                "//button[contains(text(), 'MỞ')]",
                "//div[contains(text(), 'MỞ')]",
                "//span[contains(text(), 'MỞ')]",
                ".open-btn",
                ".mo-btn"
            ]

            for selector in mo_selectors:
                try:
                    if selector.startswith('//'):
                        mo_btn = driver.find_element(By.XPATH, selector)
                    else:
                        mo_btn = driver.find_element(By.CSS_SELECTOR, selector)

                    if mo_btn.is_displayed():
                        driver.execute_script("arguments[0].scrollIntoView(true);", mo_btn)
                        time.sleep(1)
                        mo_btn.click()
                        print("✅ Đã mở hồng bao")
                        mo_button_found = True
                        time.sleep(3)
                        break
                except:
                    continue

            if not mo_button_found:
                print("⚠️  Không tìm thấy hồng bao để mở")

            # Bước 4: Đóng popup hồng bao bằng cách truy cập lại URL
            print("🔄 Đóng popup hồng bao bằng cách truy cập lại trang...")
            try:
                task_url = "https://www.13win16.com/home/<USER>"
                driver.get(task_url)
                time.sleep(3)
                print("✅ Đã truy cập lại trang để đóng popup hồng bao")
            except:
                print("⚠️  Không thể truy cập lại trang")

            self.screenshot(f"05_{username}_after_close_envelope_popup.png", driver)

            # Bước 3: Click tất cả nút "Nhận"
            print("🎁 Tìm và click nút Nhận...")
            claimed_count = 0

            nhan_selectors = [
                "//button[contains(text(), 'Nhận')]",
                "//div[contains(text(), 'Nhận')]",
                "//span[contains(text(), 'Nhận')]",
                ".claim-btn",
                ".nhan-btn",
                ".receive-btn"
            ]

            for selector in nhan_selectors:
                try:
                    if selector.startswith('//'):
                        nhan_buttons = driver.find_elements(By.XPATH, selector)
                    else:
                        nhan_buttons = driver.find_elements(By.CSS_SELECTOR, selector)

                    for btn in nhan_buttons:
                        try:
                            if btn.is_displayed() and btn.is_enabled():
                                driver.execute_script("arguments[0].scrollIntoView(true);", btn)
                                time.sleep(1)
                                btn.click()
                                claimed_count += 1
                                print(f"✅ Đã nhận thưởng {claimed_count}")
                                time.sleep(2)
                        except:
                            continue
                except:
                    continue

            self.screenshot(f"06_{username}_after_claim.png", driver)

            # Đóng popup sau khi nhận thưởng bằng dấu X
            print("❌ Đóng popup sau nhận thưởng bằng dấu X...")
            self.close_popup_with_x(driver)

            # Bước 5: Kiểm tra Lịch Sử Nhận
            print("📊 Kiểm tra Lịch Sử Nhận...")
            total_reward = self.check_reward_history(driver, username)

            print(f"🎁 Đã nhận {claimed_count} phần thưởng")
            print(f"💰 Tổng thưởng: {total_reward}D")

            return total_reward >= 13  # Tối thiểu 13D

        except Exception as e:
            print(f"❌ Lỗi xử lý hồng bao: {e}")
            return False

    def close_popup_with_x(self, driver):
        """Đóng popup bằng dấu X"""
        try:
            print("❌ Tìm dấu X để đóng popup...")
            time.sleep(2)

            # Danh sách selector cho dấu X
            x_selectors = [
                # Dấu X text
                "//button[contains(text(), '×')]",
                "//span[contains(text(), '×')]",
                "//div[contains(text(), '×')]",
                "//a[contains(text(), '×')]",

                # Dấu X Unicode khác
                "//button[contains(text(), '✕')]",
                "//span[contains(text(), '✕')]",
                "//div[contains(text(), '✕')]",

                # Class cho nút đóng
                ".close",
                ".close-btn",
                ".popup-close",
                ".modal-close",
                ".btn-close",
                "[aria-label='Close']",
                "[aria-label='close']",
                "[title='Close']",
                "[title='Đóng']",

                # SVG close icon
                "svg[class*='close']",
                "svg[class*='Close']"
            ]

            closed_count = 0

            # Thử đóng popup bằng X 3 lần
            for attempt in range(3):
                found_x = False

                for selector in x_selectors:
                    try:
                        if selector.startswith('//'):
                            elements = driver.find_elements(By.XPATH, selector)
                        else:
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)

                        for element in elements:
                            try:
                                if element.is_displayed() and element.is_enabled():
                                    # Scroll đến element
                                    driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                    time.sleep(0.5)

                                    # Click bằng JavaScript để đảm bảo
                                    driver.execute_script("arguments[0].click();", element)
                                    closed_count += 1
                                    found_x = True
                                    print(f"✅ Đã bấm dấu X {closed_count}")
                                    time.sleep(2)
                                    break
                            except:
                                continue

                        if found_x:
                            break

                    except:
                        continue

                if not found_x:
                    break

                time.sleep(1)

            if closed_count > 0:
                print(f"✅ Đã đóng {closed_count} popup bằng dấu X")
            else:
                print("ℹ️  Không tìm thấy dấu X để đóng popup")

            return closed_count > 0

        except Exception as e:
            print(f"⚠️  Lỗi đóng popup bằng X: {e}")
            return False

    def close_all_popups(self, driver):
        """Đóng tất cả popup có thể ảnh hưởng"""
        try:
            print("🔔 Đóng popup...")
            time.sleep(2)

            # Danh sách các selector popup phổ biến
            popup_selectors = [
                # Nút đóng popup
                "//button[contains(text(), 'OK')]",
                "//button[contains(text(), 'Đóng')]",
                "//button[contains(text(), 'Close')]",
                "//button[contains(text(), '×')]",
                "//span[contains(text(), '×')]",
                "//div[contains(text(), '×')]",

                # Class popup
                ".close",
                ".close-btn",
                ".popup-close",
                ".modal-close",
                ".btn-close",

                # Overlay để click đóng
                ".overlay",
                ".modal-overlay",
                ".popup-overlay"
            ]

            closed_count = 0

            # Thử đóng popup 3 lần
            for attempt in range(3):
                found_popup = False

                for selector in popup_selectors:
                    try:
                        if selector.startswith('//'):
                            elements = driver.find_elements(By.XPATH, selector)
                        else:
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)

                        for element in elements:
                            try:
                                if element.is_displayed() and element.is_enabled():
                                    driver.execute_script("arguments[0].click();", element)
                                    closed_count += 1
                                    found_popup = True
                                    print(f"✅ Đóng popup {closed_count}")
                                    time.sleep(1)
                                    break
                            except:
                                continue

                        if found_popup:
                            break

                    except:
                        continue

                if not found_popup:
                    break

                time.sleep(1)

            # Thử ESC để đóng popup còn lại
            try:
                from selenium.webdriver.common.keys import Keys
                driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                print("✅ ESC để đóng popup")
                time.sleep(1)
            except:
                pass

            if closed_count > 0:
                print(f"✅ Đã đóng {closed_count} popup")
            else:
                print("ℹ️  Không có popup cần đóng")

            return True

        except Exception as e:
            print(f"⚠️  Lỗi đóng popup: {e}")
            return False

    def check_reward_history(self, driver, username):
        """Kiểm tra lịch sử nhận thưởng"""
        try:
            print("📊 Kiểm tra Lịch Sử Nhận...")

            # Đóng popup trước khi kiểm tra bằng dấu X
            self.close_popup_with_x(driver)

            # Tìm và click "Lịch Sử Nhận"
            history_selectors = [
                "//div[contains(text(), 'Lịch Sử Nhận')]",
                "//span[contains(text(), 'Lịch Sử Nhận')]",
                "//a[contains(text(), 'Lịch Sử Nhận')]",
                "//button[contains(text(), 'Lịch Sử Nhận')]",
                ".history-tab",
                ".reward-history"
            ]

            history_found = False
            for selector in history_selectors:
                try:
                    if selector.startswith('//'):
                        history_tab = driver.find_element(By.XPATH, selector)
                    else:
                        history_tab = driver.find_element(By.CSS_SELECTOR, selector)

                    if history_tab.is_displayed():
                        driver.execute_script("arguments[0].scrollIntoView(true);", history_tab)
                        time.sleep(1)
                        history_tab.click()
                        print("✅ Đã click Lịch Sử Nhận")
                        history_found = True
                        time.sleep(3)
                        break
                except:
                    continue

            if not history_found:
                print("⚠️  Không tìm thấy Lịch Sử Nhận")
                return 0

            self.screenshot(f"07_{username}_reward_history.png", driver)

            # Đọc tổng tiền thưởng
            total_reward = 0

            # Tìm text "Tiền Thưởng" hoặc số tiền
            reward_selectors = [
                "//div[contains(text(), 'Tiền Thưởng')]",
                "//span[contains(text(), 'Tiền Thưởng')]",
                ".reward-amount",
                ".total-reward"
            ]

            for selector in reward_selectors:
                try:
                    if selector.startswith('//'):
                        elements = driver.find_elements(By.XPATH, selector)
                    else:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        text = element.text
                        # Tìm số trong text
                        import re
                        numbers = re.findall(r'\d+\.?\d*', text)
                        for num in numbers:
                            try:
                                value = float(num)
                                if value > total_reward:
                                    total_reward = value
                            except:
                                continue
                except:
                    continue

            # Nếu không tìm thấy, thử tìm trong page source
            if total_reward == 0:
                try:
                    page_source = driver.page_source
                    import re
                    # Tìm pattern "13.00", "1.16" etc
                    amounts = re.findall(r'(\d+\.?\d*)\s*D', page_source)
                    for amount in amounts:
                        try:
                            value = float(amount)
                            total_reward += value
                        except:
                            continue
                except:
                    pass

            print(f"💰 Tổng thưởng đã nhận: {total_reward}D")
            return total_reward

        except Exception as e:
            print(f"❌ Lỗi kiểm tra lịch sử: {e}")
            return 0

    def register_account(self, username, password, fullname):
        """Đăng ký tài khoản"""
        try:
            print(f"\n🚀 ĐĂNG KÝ: {username}")

            # Tạo Chrome
            driver = self.create_chrome(username)
            if not driver:
                return False

            # Đi đến trang đăng ký
            driver.get("https://www.13win16.com/home/<USER>")
            time.sleep(8)

            self.screenshot(f"01_{username}_register.png", driver)

            # Tìm input
            inputs = driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
            print(f"📝 Tìm thấy {len(inputs)} input")

            if len(inputs) < 3:
                print("❌ Không đủ input")
                return False

            # Điền form
            try:
                # Username
                inputs[0].clear()
                inputs[0].send_keys(username)
                print(f"✅ Username: {username}")
                time.sleep(1)

                # Password
                inputs[1].clear()
                inputs[1].send_keys(password)
                print(f"✅ Password")
                time.sleep(1)

                # Confirm Password
                if len(inputs) >= 3:
                    inputs[2].clear()
                    inputs[2].send_keys(password)
                    print("✅ Confirm Password")
                    time.sleep(1)

                # Fullname
                if len(inputs) >= 4:
                    inputs[3].clear()
                    inputs[3].send_keys(fullname)
                    print(f"✅ Fullname: {fullname}")
                    time.sleep(1)

            except Exception as e:
                print(f"❌ Lỗi điền form: {e}")
                return False

            self.screenshot(f"02_{username}_filled.png", driver)

            # Checkbox
            try:
                checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Checkbox")
            except:
                print("⚠️  Không có checkbox")

            # Submit
            try:
                buttons = driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    submit_btn = buttons[-1]
                    driver.execute_script("arguments[0].scrollIntoView(true);", submit_btn)
                    time.sleep(1)
                    submit_btn.click()
                    print("🚀 Submit")
                    time.sleep(8)
                else:
                    print("❌ Không tìm thấy button")
                    return False
            except Exception as e:
                print(f"❌ Lỗi submit: {e}")
                return False

            self.screenshot(f"03_{username}_result.png", driver)

            # Kiểm tra kết quả
            page_source = driver.page_source.lower()

            if any(keyword in page_source for keyword in ["thành công", "success", "welcome"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")

                # Xử lý hồng bao và nhiệm vụ
                reward_sufficient = self.handle_red_envelope_and_tasks(driver, username)

                # Lưu tài khoản
                with open("accounts.txt", "a", encoding="utf-8") as f:
                    f.write(f"{username}|{password}|{fullname}\n")

                if reward_sufficient:
                    print(f"💰 Tài khoản đã đạt tối thiểu 13D ✅")
                else:
                    print(f"💰 Tài khoản có ít hơn 13D, cần kiểm tra thủ công")

                return True

            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already"]):
                print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                return False
            else:
                print(f"{Fore.RED}❓ Không rõ kết quả{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"❌ Lỗi đăng ký: {e}")
            return False

    def generate_password(self):
        """Tạo password ngẫu nhiên"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(8)) + "123"

    def run_registration(self):
        """Chạy đăng ký"""
        print("🎯 TOOL ĐĂNG KÝ DỄ DÀNG")
        print("="*40)

        # Nhập thông tin
        prefix = input("👤 Prefix username: ").strip() or "taolatrumnohu"
        try:
            count = int(input("📝 Số lượng tài khoản: "))
            start = int(input("📝 Bắt đầu từ số: ") or "1")
        except:
            print("❌ Số không hợp lệ!")
            return

        fullname = "TRAN HOANG AN"

        print(f"\n📋 Sẽ đăng ký {count} tài khoản:")
        print(f"👤 {prefix}{start} → {prefix}{start+count-1}")
        print(f"👨 Họ tên: {fullname}")
        print(f"🌐 Mỗi tài khoản = 1 Chrome riêng")

        if input("\nTiếp tục? (y/n): ").lower() != 'y':
            return

        success_count = 0
        failed_count = 0

        try:
            for i in range(count):
                num = start + i
                username = f"{prefix}{num}"
                password = self.generate_password()

                print(f"\n📊 {i+1}/{count}: {username}")

                if self.register_account(username, password, fullname):
                    success_count += 1
                    print(f"{Fore.GREEN}✅ Thành công: {success_count}{Style.RESET_ALL}")
                else:
                    failed_count += 1
                    print(f"{Fore.RED}❌ Thất bại: {failed_count}{Style.RESET_ALL}")

                # Delay giữa các lần đăng ký
                if i < count - 1:
                    delay = random.randint(3, 8)
                    print(f"⏳ Chờ {delay}s...")
                    time.sleep(delay)

            # Kết quả cuối
            print(f"\n📊 KẾT QUẢ CUỐI:")
            print(f"✅ Đăng ký thành công: {success_count}")
            print(f"❌ Đăng ký thất bại: {failed_count}")
            print(f"🌐 Chrome đang chạy: {len(self.active_drivers)}")
            print(f"📁 Tài khoản lưu trong: accounts.txt")
            print(f"📸 Screenshot trong: {self.screenshot_folder}/")

            if success_count > 0:
                print(f"\n💰 TÌNH TRẠNG THƯỞNG:")
                print(f"📊 Đã tự động xử lý hồng bao và nhiệm vụ")
                print(f"🎯 Mỗi tài khoản đã kiểm tra tối thiểu 13D")
                print(f"📱 Để đạt 52D cần thêm thưởng từ app (+35D)")

                print(f"\n{Fore.YELLOW}📱 HƯỚNG DẪN NHẬN THÊM THƯỞNG:{Style.RESET_ALL}")
                print("1. Tải app 13win trên điện thoại/BlueStacks")
                print("2. Đăng nhập từng tài khoản trong app")
                print("3. Nhận thưởng app (+35D)")
                print("4. Tổng: 13D + 35D = 48D+ ✅")

                print(f"\n{Fore.GREEN}🎉 QUY TRÌNH HOÀN CHỈNH:{Style.RESET_ALL}")
                print("→ Đã xử lý hồng bao tự động")
                print("→ Đã nhận thưởng nhiệm vụ web")
                print("→ Kiểm tra Lịch Sử Nhận để xác nhận")
                print("→ Cần app để đạt đủ 52D")

        except KeyboardInterrupt:
            print(f"\n⚠️  Đã dừng bởi người dùng!")

        finally:
            # Hỏi đóng Chrome
            if self.active_drivers:
                print(f"\n🌐 Hiện có {len(self.active_drivers)} Chrome đang chạy")
                choice = input(f"{Fore.CYAN}Đóng tất cả Chrome? (y/n): {Style.RESET_ALL}").lower()

                if choice == 'y':
                    self.close_all_chrome()
                else:
                    print("🌐 Chrome vẫn mở để bạn kiểm tra thủ công")
                    print("💡 Mỗi Chrome đang ở trang thưởng, sẵn sàng nhận thưởng")

    def close_all_chrome(self):
        """Đóng tất cả Chrome"""
        for username, driver in self.active_drivers.items():
            try:
                driver.quit()
                print(f"✅ Đã đóng Chrome: {username}")
            except:
                pass
        self.active_drivers.clear()
        print("🔒 Đã đóng tất cả Chrome")

def main():
    """Main function"""
    try:
        tool = EasyTool()
        tool.run_registration()
    except Exception as e:
        print(f"❌ Lỗi chính: {e}")

if __name__ == "__main__":
    main()
