"""
Debug phân tích form đăng ký
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def debug_form_analysis():
    """Debug phân tích form đăng ký"""
    print("🔍 DEBUG PHÂN TÍCH FORM ĐĂNG KÝ")
    print("="*50)
    
    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')
        
        # Sử dụng ChromeDriver local
        chromedriver_path = "drivers/chromedriver.exe"
        if os.path.exists(chromedriver_path):
            print(f"✅ Sử dụng ChromeDriver local: {chromedriver_path}")
            service = Service(chromedriver_path)
        else:
            print("❌ Không tìm thấy ChromeDriver local, sử dụng WebDriverManager")
            from webdriver_manager.chrome import ChromeDriverManager
            service = Service(ChromeDriverManager().install())
        
        # Tạo driver
        print("🚀 Đang tạo browser...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(10)
        
        print("✅ Browser đã tạo thành công")
        
        # Điều hướng đến trang đăng ký
        url = "https://www.13win16.com/home/<USER>"
        print(f"🌐 Đang điều hướng đến: {url}")
        
        driver.get(url)
        print("✅ Đã điều hướng thành công")
        
        # Chờ trang load
        print("⏳ Chờ trang load...")
        time.sleep(5)
        
        # Lấy title
        try:
            title = driver.title
            print(f"📄 Title trang: {title}")
        except:
            print("⚠️  Không thể lấy title")
        
        # Lấy URL hiện tại
        try:
            current_url = driver.current_url
            print(f"🔗 URL hiện tại: {current_url}")
        except:
            print("⚠️  Không thể lấy URL hiện tại")
        
        # Kiểm tra page source
        try:
            page_source = driver.page_source
            print(f"📝 Page source length: {len(page_source)} characters")
            
            # Lưu page source
            with open("debug_page_source.html", "w", encoding="utf-8") as f:
                f.write(page_source)
            print("✅ Đã lưu page source: debug_page_source.html")
            
        except Exception as e:
            print(f"❌ Lỗi khi lấy page source: {e}")
        
        # Tìm các element form
        print("\n🔍 Tìm các element form...")
        
        # Các selector có thể có
        selectors_to_test = [
            ("input[type='text']", "Text inputs"),
            ("input[type='password']", "Password inputs"),
            ("input[name*='user']", "Username inputs"),
            ("input[name*='pass']", "Password inputs"),
            ("input[name*='name']", "Name inputs"),
            ("input[name*='phone']", "Phone inputs"),
            ("input[name*='email']", "Email inputs"),
            ("button[type='submit']", "Submit buttons"),
            ("input[type='submit']", "Submit inputs"),
            ("input[type='checkbox']", "Checkboxes"),
            ("form", "Forms"),
            (".form", "Form classes"),
            ("#register", "Register ID"),
            (".register", "Register classes")
        ]
        
        found_elements = []
        
        for selector, description in selectors_to_test:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"✅ {description}: {len(elements)} element(s)")
                    for i, element in enumerate(elements[:3]):  # Chỉ hiển thị 3 đầu tiên
                        try:
                            tag = element.tag_name
                            name = element.get_attribute('name') or 'N/A'
                            id_attr = element.get_attribute('id') or 'N/A'
                            placeholder = element.get_attribute('placeholder') or 'N/A'
                            print(f"   [{i+1}] Tag: {tag}, Name: {name}, ID: {id_attr}, Placeholder: {placeholder}")
                            found_elements.append({
                                'selector': selector,
                                'element': element,
                                'name': name,
                                'id': id_attr
                            })
                        except:
                            pass
                else:
                    print(f"❌ {description}: Không tìm thấy")
            except Exception as e:
                print(f"❌ Lỗi khi tìm {description}: {e}")
        
        # Tóm tắt
        print(f"\n📊 Tóm tắt: Tìm thấy {len(found_elements)} element(s)")
        
        if found_elements:
            print(f"{Fore.GREEN}✅ Có thể phân tích form được!{Style.RESET_ALL}")
        else:
            print(f"{Fore.RED}❌ Không tìm thấy element form nào!{Style.RESET_ALL}")
            print("Có thể do:")
            print("1. Trang web sử dụng JavaScript để load form")
            print("2. Form được load bằng AJAX")
            print("3. Website có protection chống bot")
            print("4. Cần chờ lâu hơn để trang load")
        
        # Chụp screenshot
        try:
            driver.save_screenshot("debug_screenshot.png")
            print("📸 Đã chụp screenshot: debug_screenshot.png")
        except:
            print("⚠️  Không thể chụp screenshot")
        
        return len(found_elements) > 0
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False
        
    finally:
        if driver:
            try:
                driver.quit()
                print("🔒 Đã đóng browser")
            except:
                pass

def main():
    """Main function"""
    try:
        success = debug_form_analysis()
        
        if success:
            print(f"\n{Fore.GREEN}🎉 DEBUG THÀNH CÔNG!{Style.RESET_ALL}")
            print("Kiểm tra các file:")
            print("- debug_page_source.html")
            print("- debug_screenshot.png")
        else:
            print(f"\n{Fore.RED}❌ DEBUG THẤT BẠI!{Style.RESET_ALL}")
            print("Cần kiểm tra thêm về website hoặc network")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
