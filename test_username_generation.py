"""
Test tính năng tạo username tuần tự
"""

from account_generator import AccountGenerator
from config import ACCOUNT_CONFIG
import os

def test_sequential_username():
    """Test tạo username tuần tự"""
    print("🧪 TEST TẠO USERNAME TUẦN TỰ")
    print("="*50)
    
    # Xóa file counter cũ để test từ đầu
    if os.path.exists(ACCOUNT_CONFIG['username_counter_file']):
        os.remove(ACCOUNT_CONFIG['username_counter_file'])
        print("✅ Đã xóa file counter cũ")
    
    generator = AccountGenerator()
    
    print(f"📋 Prefix: {ACCOUNT_CONFIG['username_prefix']}")
    print(f"📋 Họ tên cố định: {ACCOUNT_CONFIG['fixed_real_name']}")
    print()
    
    # Test tạo 5 tài khoản tuần tự
    print("🔄 Tạo 5 tài khoản tuần tự:")
    for i in range(5):
        account = generator.generate_complete_account(use_sequential_username=True)
        print(f"  {i+1}. Username: {account['username']} | Real Name: {account['full_name']}")
    
    print()
    
    # Test tạo username ngẫu nhiên
    print("🎲 Tạo 3 username ngẫu nhiên (khi tài khoản đã tồn tại):")
    for i in range(3):
        account = generator.generate_account_for_retry()
        print(f"  {i+1}. Username: {account['username']} | Real Name: {account['full_name']}")
    
    print()
    
    # Test tiếp tục với username tuần tự
    print("🔄 Tiếp tục với username tuần tự:")
    for i in range(2):
        account = generator.generate_complete_account(use_sequential_username=True)
        print(f"  {i+1}. Username: {account['username']} | Real Name: {account['full_name']}")
    
    print()
    print("✅ Test hoàn thành!")
    
    # Hiển thị nội dung file counter
    if os.path.exists(ACCOUNT_CONFIG['username_counter_file']):
        with open(ACCOUNT_CONFIG['username_counter_file'], 'r') as f:
            counter = f.read().strip()
        print(f"📊 Counter hiện tại: {counter}")

def test_manual_account():
    """Test tài khoản thủ công"""
    print("\n🧪 TEST TÀI KHOẢN THỦ CÔNG")
    print("="*50)
    
    manual_info = {
        'username': 'testuser123',
        'password': 'TestPass123!',
        'full_name': ACCOUNT_CONFIG['fixed_real_name'],  # Sử dụng họ tên cố định
        'phone': '**********'
    }
    
    print("📋 Thông tin thủ công:")
    for key, value in manual_info.items():
        print(f"  {key}: {value}")
    
    print("\n✅ Tài khoản thủ công sẽ sử dụng họ tên cố định từ config")

def main():
    """Main function"""
    try:
        test_sequential_username()
        test_manual_account()
        
        print(f"\n📁 File counter: {ACCOUNT_CONFIG['username_counter_file']}")
        print("📁 Các tài khoản sẽ được lưu vào thư mục output/")
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    input("\nNhấn Enter để thoát...")

if __name__ == "__main__":
    main()
