"""
Script hiển thị log real-time
"""

import os
import time
import sys
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def tail_log_file(filename, lines=50):
    """Hiển thị log file real-time"""
    if not os.path.exists(filename):
        print(f"{Fore.RED}File log không tồn tại: {filename}{Style.RESET_ALL}")
        return
    
    print(f"{Fore.GREEN}📋 Hiển thị log real-time: {filename}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}Nhấn Ctrl+C để thoát{Style.RESET_ALL}")
    print("="*80)
    
    # Đọc n dòng cuối
    with open(filename, 'r', encoding='utf-8') as f:
        lines_list = f.readlines()
        if len(lines_list) > lines:
            lines_list = lines_list[-lines:]
        
        for line in lines_list:
            print_colored_log(line.strip())
    
    # Theo dõi file real-time
    with open(filename, 'r', encoding='utf-8') as f:
        # Đi đến cuối file
        f.seek(0, 2)
        
        try:
            while True:
                line = f.readline()
                if line:
                    print_colored_log(line.strip())
                else:
                    time.sleep(0.1)
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}Đã dừng theo dõi log{Style.RESET_ALL}")

def print_colored_log(line):
    """In log với màu sắc"""
    if not line:
        return
    
    # Màu sắc theo level
    if "ERROR" in line:
        print(f"{Fore.RED}{line}{Style.RESET_ALL}")
    elif "WARNING" in line:
        print(f"{Fore.YELLOW}{line}{Style.RESET_ALL}")
    elif "INFO" in line:
        if "thành công" in line.lower() or "success" in line.lower():
            print(f"{Fore.GREEN}{line}{Style.RESET_ALL}")
        elif "proxy hoạt động" in line.lower():
            print(f"{Fore.CYAN}{line}{Style.RESET_ALL}")
        else:
            print(f"{Fore.WHITE}{line}{Style.RESET_ALL}")
    else:
        print(line)

def main():
    """Main function"""
    log_files = [
        "registration.log",
        "output/registration.log",
        "logs/registration.log"
    ]
    
    # Tìm file log
    log_file = None
    for file in log_files:
        if os.path.exists(file):
            log_file = file
            break
    
    if not log_file:
        print(f"{Fore.RED}Không tìm thấy file log nào!{Style.RESET_ALL}")
        print("Các file đã tìm:")
        for file in log_files:
            print(f"  - {file}")
        
        # Tạo file log mẫu
        print(f"\n{Fore.YELLOW}Tạo file log mẫu...{Style.RESET_ALL}")
        with open("registration.log", "w", encoding="utf-8") as f:
            f.write("2025-05-27 12:00:00,000 - INFO - Tool khởi động\n")
        log_file = "registration.log"
    
    try:
        tail_log_file(log_file)
    except Exception as e:
        print(f"{Fore.RED}Lỗi: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
