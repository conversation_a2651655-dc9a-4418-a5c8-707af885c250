# 🖥️ **13WIN ACCOUNT MANAGER - GUI TOOL**

## 🎯 **TỔNG QUAN**

Tool quản lý tài khoản 13win với giao diện đồ họa (GUI) được viết bằng C# Windows Forms. Tool này giúp quản lý trạng thái các tài khoản từ file `accounts.txt` và theo dõi tiến trình đăng ký.

## ✨ **TÍNH NĂNG CHÍNH**

### 📊 **Quản lý tài khoản:**
- ✅ **Hiển thị danh sách tài khoản** từ file `accounts.txt`
- ✅ **Thêm/Sửa/Xóa tài khoản** trực tiếp trong GUI
- ✅ **Import/Export** file accounts.txt
- ✅ **Theo dõi trạng thái** từng tài khoản

### 🎮 **Trạng thái tài khoản:**
- 🔘 **Ch<PERSON><PERSON> bắt đầu** (Gray)
- 🟠 **<PERSON><PERSON> đăng ký** (Orange)
- 🔵 **Đăng ký thành công** (Blue)
- 🔴 **Đăng ký thất bại** (Red)
- 🟢 **Đã nhận thưởng web** (Green)
- 🟣 **Đã nhận thưởng app** (Purple)
- 🟢 **Hoàn thành** (Dark Green)
- 🔴 **Lỗi** (Red)

### 💰 **Theo dõi thưởng:**
- 💵 **Thưởng Web** (từ Chrome)
- 📱 **Thưởng App** (từ BlueStacks)
- 💎 **Tổng thưởng** (Web + App)
- 🎯 **Mục tiêu 52D** tự động đánh dấu hoàn thành

### 🔧 **Tích hợp tool Python:**
- 🚀 **Chạy tool đăng ký** trực tiếp từ GUI
- 📊 **Theo dõi tiến trình** real-time
- 📸 **Monitor screenshots** để cập nhật trạng thái
- 🔄 **Auto refresh** mỗi 5 giây

## 🛠️ **CÀI ĐẶT**

### 📋 **Yêu cầu:**
- ✅ **.NET 6.0** hoặc cao hơn
- ✅ **Windows 10/11**
- ✅ **Visual Studio 2022** (để build)

### 🔧 **Build và chạy:**

```bash
# Clone hoặc tải source code
cd AccountManager

# Restore packages
dotnet restore

# Build project
dotnet build

# Chạy ứng dụng
dotnet run
```

### 📦 **Build executable:**

```bash
# Build release
dotnet publish -c Release -r win-x64 --self-contained

# File .exe sẽ ở: bin/Release/net6.0-windows/win-x64/publish/
```

## 🚀 **CÁCH SỬ DỤNG**

### 1️⃣ **Khởi động ứng dụng:**
```bash
AccountManager.exe
```

### 2️⃣ **Import tài khoản:**
- **File → Import từ file** → Chọn `accounts.txt`
- Hoặc **thêm tài khoản thủ công** qua nút "Thêm"

### 3️⃣ **Chạy tool đăng ký:**
- **Tools → Chạy tool đăng ký**
- Tool Python sẽ được khởi chạy tự động
- GUI sẽ theo dõi và cập nhật trạng thái

### 4️⃣ **Theo dõi tiến trình:**
- **Trạng thái** được cập nhật real-time
- **Thưởng** được tính tự động
- **Màu sắc** thể hiện trạng thái hiện tại

### 5️⃣ **Quản lý tài khoản:**
- **Double-click** để sửa tài khoản
- **Chuột phải** để xem menu context
- **Checkbox** để chọn nhiều tài khoản

## 📊 **GIAO DIỆN**

### 🖥️ **Main Window:**
```
┌─────────────────────────────────────────────────────────────┐
│ File  Tools  View                                           │
├─────────────────────────────────────────────────────────────┤
│ [Thêm] [Sửa] [Xóa] │ [Chạy tool] [Làm mới]                 │
├─────────────────────────────────────────────────────────────┤
│ ☐ │ Username │ Password │ Real Name │ Status │ Web │ App │...│
│ ☐ │ taolatr1 │ ******** │ TRAN...   │ 🟢     │ 13D │ 35D │...│
│ ☐ │ taolatr2 │ ******** │ TRAN...   │ 🔵     │ 13D │ 0D  │...│
│ ☐ │ taolatr3 │ ******** │ TRAN...   │ 🟠     │ 0D  │ 0D  │...│
├─────────────────────────────────────────────────────────────┤
│ Tổng: 5 │ Hoàn thành: 1 │ Thành công: 2 │ Tổng thưởng: 61D │
└─────────────────────────────────────────────────────────────┘
```

### ✏️ **Edit Account Dialog:**
```
┌─────────────────────────────────┐
│ Sửa tài khoản                   │
├─────────────────────────────────┤
│ Tài khoản:  [taolatrumnohu1   ] │
│ Mật khẩu:   [**************** ] │
│ Tên thật:   [TRAN HOANG AN    ] │
│ Trạng thái: [Hoàn thành      ▼] │
│ Thưởng Web: [13.00           ] │
│ Thưởng App: [35.00           ] │
│ Ghi chú:    [                 ] │
│             [                 ] │
├─────────────────────────────────┤
│                    [OK] [Cancel] │
└─────────────────────────────────┘
```

## 📁 **CẤU TRÚC PROJECT**

```
AccountManager/
├── AccountManager.csproj          # Project file
├── Program.cs                     # Entry point
├── Models/
│   └── Account.cs                 # Account model & enums
├── Services/
│   ├── AccountService.cs          # Business logic
│   └── StatusMonitor.cs           # Monitor tool progress
├── Forms/
│   ├── MainForm.cs                # Main window
│   └── AccountEditForm.cs         # Edit dialog
└── README_GUI.md                  # This file
```

## 🔄 **TÍCH HỢP VỚI PYTHON TOOL**

### 📂 **Files được sử dụng:**
- **`accounts.txt`** - Danh sách tài khoản (input)
- **`account_status.json`** - Trạng thái tài khoản (output)
- **`screenshots/`** - Screenshots từ tool Python

### 🔄 **Quy trình tích hợp:**
1. **GUI đọc** `accounts.txt` và `account_status.json`
2. **User click "Chạy tool"** → GUI khởi chạy `python easy_tool.py`
3. **Tool Python chạy** → Tạo screenshots và cập nhật JSON
4. **GUI monitor** file changes → Auto refresh trạng thái
5. **Tool Python hoàn thành** → GUI hiển thị kết quả cuối

### 📊 **Status mapping:**
```csharp
// Python tool → GUI status
"01_username_registration.png" → Registering
"02_username_success.png"      → RegisterSuccess  
"03_username_failed.png"       → RegisterFailed
"04_username_task_page.png"    → WebRewardClaimed
"05_username_after_claim.png"  → AppRewardClaimed
```

## ⚙️ **CẤU HÌNH**

### 🔧 **Tùy chỉnh đường dẫn:**
```csharp
// Trong AccountService.cs
private readonly string _accountsFilePath = "accounts.txt";
private readonly string _statusFilePath = "account_status.json";

// Trong MainForm.cs - RunRegistrationTool_Click
processInfo.FileName = "python";
processInfo.Arguments = "easy_tool.py";
```

### 🎨 **Tùy chỉnh màu sắc:**
```csharp
// Trong Account.cs - GetStatusColor()
AccountStatus.Completed => Color.DarkGreen,
AccountStatus.RegisterSuccess => Color.Blue,
AccountStatus.RegisterFailed => Color.Red,
// ...
```

## 🐛 **XỬ LÝ LỖI**

### ❌ **Lỗi thường gặp:**

**1. Không tìm thấy Python:**
```
Lỗi chạy tool: The system cannot find the file specified
```
**Giải pháp:** Cài đặt Python và thêm vào PATH

**2. Không đọc được accounts.txt:**
```
Lỗi load tài khoản: Access denied
```
**Giải pháp:** Chạy với quyền Administrator

**3. JSON format lỗi:**
```
Lỗi lưu trạng thái: Invalid JSON
```
**Giải pháp:** Xóa file `account_status.json` và khởi động lại

## 🚀 **TÍNH NĂNG NÂNG CAO**

### 🔄 **Auto-refresh:**
- GUI tự động làm mới mỗi 5 giây
- Monitor file changes real-time
- Cập nhật trạng thái từ screenshots

### 📊 **Statistics:**
- Đếm tài khoản theo trạng thái
- Tính tổng thưởng
- Hiển thị progress bar

### 🎯 **Filtering:**
- Hiện tất cả tài khoản
- Chỉ hiện chưa hoàn thành
- Filter theo trạng thái

### 💾 **Data persistence:**
- Lưu trạng thái vào JSON
- Backup tự động
- Import/Export dễ dàng

## 🎉 **KẾT QUẢ**

**Tool GUI cung cấp:**
- ✅ **Giao diện trực quan** dễ sử dụng
- ✅ **Quản lý tài khoản** hiệu quả
- ✅ **Theo dõi tiến trình** real-time
- ✅ **Tích hợp tool Python** seamless
- ✅ **Báo cáo thống kê** chi tiết
- ✅ **Xử lý lỗi** robust

**Phù hợp cho:**
- 👥 **Quản lý nhiều tài khoản**
- 📊 **Theo dõi tiến trình đăng ký**
- 💰 **Kiểm soát thưởng**
- 🎯 **Đạt mục tiêu 52D**
