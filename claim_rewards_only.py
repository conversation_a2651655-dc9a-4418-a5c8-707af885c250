"""
Tool chỉ để nhận thưởng cho tài khoản đã có
"""

import time
import random
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def login_and_claim_rewards(username, password):
    """Đăng nhập và nhận thưởng"""
    print(f"\n🔑 ĐĂNG NHẬP VÀ NHẬN THƯỞNG: {username}")
    print("="*60)
    
    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')
        
        # Sử dụng ChromeDriver local
        chromedriver_path = "drivers/chromedriver.exe"
        service = Service(chromedriver_path)
        
        # Tạo driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(5)
        
        # BƯỚC 1: Đăng nhập
        print("🔑 BƯỚC 1: Đăng nhập")
        
        # Điều hướng đến trang đăng nhập
        login_url = "https://www.13win16.com/home/<USER>"
        driver.get(login_url)
        time.sleep(3)
        
        # Điền form đăng nhập
        try:
            # Username
            username_field = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder*="điện thoại"], input[placeholder*="username"], input[type="text"]'))
            )
            username_field.clear()
            username_field.send_keys(username)
            print(f"✅ Username: {username}")
            time.sleep(1)
            
            # Password
            password_field = driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
            password_field.clear()
            password_field.send_keys(password)
            print(f"✅ Password: {password}")
            time.sleep(1)
            
            # Submit đăng nhập
            login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"], button:contains("Đăng nhập"), .login-btn')
            login_button.click()
            print("🚀 Đã submit đăng nhập...")
            
            # Chờ kết quả đăng nhập
            time.sleep(5)
            
            # Kiểm tra đăng nhập thành công
            page_source = driver.page_source.lower()
            
            if any(keyword in page_source for keyword in ["dashboard", "profile", "logout", "đăng xuất", "tài khoản"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG NHẬP THÀNH CÔNG!{Style.RESET_ALL}")
                
                # BƯỚC 2: Nhận thưởng
                print(f"\n🎁 BƯỚC 2: Nhận thưởng")
                
                if claim_rewards(driver):
                    print(f"{Fore.GREEN}🎁 ĐÃ NHẬN THƯỞNG THÀNH CÔNG!{Style.RESET_ALL}")
                    return True
                else:
                    print(f"{Fore.YELLOW}⚠️  Không thể nhận thưởng{Style.RESET_ALL}")
                    return True  # Vẫn coi là thành công vì đăng nhập OK
                    
            else:
                print(f"{Fore.RED}❌ ĐĂNG NHẬP THẤT BẠI!{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Lỗi đăng nhập: {e}{Style.RESET_ALL}")
            return False
        
    except Exception as e:
        print(f"{Fore.RED}❌ Lỗi: {e}{Style.RESET_ALL}")
        return False
        
    finally:
        if driver:
            try:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                driver.quit()
            except:
                pass

def claim_rewards(driver, user_id="391111507"):
    """Tự động nhận thưởng từ nhiệm vụ"""
    try:
        # URL trang nhiệm vụ
        task_url = f"https://www.13win16.com/home/<USER>"
        
        print(f"🎁 Đi đến trang nhiệm vụ...")
        driver.get(task_url)
        time.sleep(3)
        
        print(f"📄 URL: {task_url}")
        
        # Tìm tất cả nút "Nhận"
        claim_buttons = []
        
        # Các selector có thể cho nút "Nhận"
        selectors = [
            "//button[contains(text(), 'Nhận')]",
            "//button[contains(@class, 'green')]",
            "//button[contains(@class, 'success')]",
            "//button[contains(@style, 'green')]",
            "//button[contains(@style, 'rgb(34, 197, 94)')]"
        ]
        
        print("🔍 Tìm các nút 'Nhận'...")
        
        for selector in selectors:
            try:
                buttons = driver.find_elements(By.XPATH, selector)
                for btn in buttons:
                    if btn not in claim_buttons and btn.is_displayed():
                        claim_buttons.append(btn)
            except:
                pass
        
        print(f"📊 Tổng cộng tìm thấy: {len(claim_buttons)} nút có thể nhận")
        
        if not claim_buttons:
            print("⚠️  Không tìm thấy nút 'Nhận' nào")
            
            # Chụp screenshot để debug
            try:
                driver.save_screenshot("no_claim_buttons.png")
                print("📸 Đã chụp screenshot: no_claim_buttons.png")
            except:
                pass
            
            return False
        
        # Click từng nút "Nhận"
        claimed_count = 0
        
        for i, button in enumerate(claim_buttons):
            try:
                # Kiểm tra nút có thể click không
                if button.is_enabled() and button.is_displayed():
                    # Scroll đến nút
                    driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(1)
                    
                    # Lấy text của nút để xác nhận
                    button_text = button.text
                    print(f"🎯 Chuẩn bị click nút {i+1}: '{button_text}'")
                    
                    # Click nút
                    button.click()
                    claimed_count += 1
                    
                    print(f"✅ Đã click nút {i+1}: Nhận thưởng")
                    
                    # Chờ một chút giữa các lần click
                    time.sleep(random.uniform(2, 4))
                    
                else:
                    print(f"⚠️  Nút {i+1} không thể click (đã nhận hoặc chưa đủ điều kiện)")
                    
            except Exception as e:
                print(f"❌ Lỗi khi click nút {i+1}: {e}")
        
        print(f"\n📊 Kết quả nhận thưởng:")
        print(f"✅ Đã nhận: {claimed_count} phần thưởng")
        
        # Chụp screenshot sau khi nhận thưởng
        try:
            driver.save_screenshot("after_claim_rewards.png")
            print("📸 Đã chụp screenshot: after_claim_rewards.png")
        except:
            pass
        
        return claimed_count > 0
        
    except Exception as e:
        print(f"❌ Lỗi khi nhận thưởng: {e}")
        return False

def read_accounts_from_file():
    """Đọc tài khoản từ file successful_accounts.txt"""
    accounts = []
    
    try:
        with open("successful_accounts.txt", "r", encoding="utf-8") as f:
            content = f.read()
            
        # Parse tài khoản
        blocks = content.split("-" * 50)
        
        for block in blocks:
            if "Username:" in block and "Password:" in block:
                lines = block.strip().split("\n")
                username = None
                password = None
                
                for line in lines:
                    if line.startswith("Username:"):
                        username = line.split(":", 1)[1].strip()
                    elif line.startswith("Password:"):
                        password = line.split(":", 1)[1].strip()
                
                if username and password:
                    accounts.append((username, password))
        
        return accounts
        
    except FileNotFoundError:
        print("❌ Không tìm thấy file successful_accounts.txt")
        return []
    except Exception as e:
        print(f"❌ Lỗi đọc file: {e}")
        return []

def main():
    """Main function"""
    try:
        print("🎁 TOOL NHẬN THƯỞNG CHO TÀI KHOẢN ĐÃ CÓ")
        print("="*60)
        print("Tool này sẽ:")
        print("1. Đọc tài khoản từ successful_accounts.txt")
        print("2. Đăng nhập từng tài khoản")
        print("3. Tự động nhận thưởng từ trang nhiệm vụ")
        
        # Đọc tài khoản
        accounts = read_accounts_from_file()
        
        if not accounts:
            print("❌ Không có tài khoản nào để xử lý!")
            return
        
        print(f"\n📊 Tìm thấy {len(accounts)} tài khoản:")
        for i, (username, password) in enumerate(accounts[:5]):  # Hiển thị 5 tài khoản đầu
            print(f"  {i+1}. {username}")
        
        if len(accounts) > 5:
            print(f"  ... và {len(accounts) - 5} tài khoản khác")
        
        confirm = input(f"\n{Fore.YELLOW}Tiếp tục nhận thưởng cho tất cả? (y/n): {Style.RESET_ALL}").lower()
        
        if confirm == 'y':
            success_count = 0
            failed_count = 0
            
            for i, (username, password) in enumerate(accounts):
                print(f"\n📊 Tiến trình: {i+1}/{len(accounts)}")
                
                try:
                    if login_and_claim_rewards(username, password):
                        success_count += 1
                        print(f"{Fore.GREEN}✅ Thành công: {success_count}/{i+1}{Style.RESET_ALL}")
                    else:
                        failed_count += 1
                        print(f"{Fore.RED}❌ Thất bại: {failed_count}/{i+1}{Style.RESET_ALL}")
                except Exception as e:
                    failed_count += 1
                    print(f"{Fore.RED}❌ Lỗi: {e}{Style.RESET_ALL}")
                
                # Delay giữa các tài khoản
                if i < len(accounts) - 1:
                    delay = random.randint(5, 10)
                    print(f"⏳ Chờ {delay}s trước tài khoản tiếp theo...")
                    time.sleep(delay)
            
            # Tóm tắt
            print(f"\n📊 KẾT QUẢ CUỐI CÙNG:")
            print(f"✅ Thành công: {success_count}")
            print(f"❌ Thất bại: {failed_count}")
            
        else:
            print("❌ Đã hủy!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
