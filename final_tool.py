"""
TOOL DUY NHẤT - ĐĂNG KÝ + NHẬN THƯỞNG 13WIN
<PERSON>l hoàn chỉnh, đ<PERSON><PERSON>, dễ sử dụng
"""

import time
import random
import string
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class FinalTool:
    def __init__(self):
        self.driver = None
        self.chromedriver_path = "drivers/chromedriver.exe"
        self.screenshot_folder = "chụp màn hình"

        # Tạo folder screenshot nếu chưa có
        if not os.path.exists(self.screenshot_folder):
            os.makedirs(self.screenshot_folder)
            print(f"📁 Đã tạo folder: {self.screenshot_folder}")

    def save_screenshot(self, filename):
        """Lưu screenshot vào folder chụp màn hình"""
        try:
            filepath = os.path.join(self.screenshot_folder, filename)
            self.driver.save_screenshot(filepath)
            print(f"📸 Screenshot: {filename}")
            return True
        except Exception as e:
            print(f"⚠️  Lỗi chụp màn hình: {e}")
            return False

    def setup_driver(self):
        """Khởi tạo Chrome driver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            service = Service(self.chromedriver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(3)

            print("✅ Đã khởi tạo browser")
            return True

        except Exception as e:
            print(f"❌ Lỗi khởi tạo: {e}")
            return False

    def generate_password(self):
        """Tạo mật khẩu ngẫu nhiên"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(8)) + "123"

    def handle_red_envelope(self, username):
        """Xử lý hồng bao sau khi đăng ký thành công"""
        try:
            print("🧧 Tìm hồng bao...")

            # Chờ một chút để hồng bao xuất hiện
            time.sleep(3)

            # Các selector có thể cho hồng bao/popup thưởng
            red_envelope_selectors = [
                # Popup/Modal
                '.modal',
                '.popup',
                '.dialog',
                '.overlay',

                # Hồng bao/Gift
                '.red-envelope',
                '.gift',
                '.reward',
                '.bonus',

                # Button trong popup
                '.modal button',
                '.popup button',
                '.dialog button',

                # Text có thể click
                "//div[contains(text(), 'nhận')]",
                "//div[contains(text(), 'Nhận')]",
                "//button[contains(text(), 'nhận')]",
                "//button[contains(text(), 'Nhận')]",
                "//span[contains(text(), 'nhận')]",
                "//span[contains(text(), 'Nhận')]",

                # Hình ảnh hồng bao
                "//img[contains(@src, 'envelope')]",
                "//img[contains(@src, 'gift')]",
                "//img[contains(@src, 'reward')]",

                # Div có thể click
                "//div[contains(@class, 'envelope')]",
                "//div[contains(@class, 'gift')]",
                "//div[contains(@class, 'reward')]"
            ]

            found_envelope = False

            for selector in red_envelope_selectors:
                try:
                    if selector.startswith('//'):
                        # XPath selector
                        elements = self.driver.find_elements(By.XPATH, selector)
                    else:
                        # CSS selector
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                    for element in elements:
                        try:
                            if element.is_displayed() and element.is_enabled():
                                # Chụp màn hình trước khi click hồng bao
                                self.save_screenshot(f"04_red_envelope_found_{username}.png")

                                # Scroll đến element
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                time.sleep(1)

                                # Click hồng bao
                                element.click()
                                print(f"✅ Đã click hồng bao: {selector}")

                                # Chờ và chụp màn hình sau khi click
                                time.sleep(2)
                                self.save_screenshot(f"05_red_envelope_clicked_{username}.png")

                                found_envelope = True
                                break
                        except Exception as e:
                            continue

                    if found_envelope:
                        break

                except Exception as e:
                    continue

            if found_envelope:
                # Tìm và click nút "OK" hoặc "Đóng" nếu có
                try:
                    time.sleep(2)
                    close_selectors = [
                        "//button[contains(text(), 'OK')]",
                        "//button[contains(text(), 'Đóng')]",
                        "//button[contains(text(), 'Close')]",
                        "//span[contains(text(), 'OK')]",
                        ".modal-close",
                        ".popup-close",
                        ".close-btn"
                    ]

                    for selector in close_selectors:
                        try:
                            if selector.startswith('//'):
                                element = self.driver.find_element(By.XPATH, selector)
                            else:
                                element = self.driver.find_element(By.CSS_SELECTOR, selector)

                            if element.is_displayed():
                                element.click()
                                print("✅ Đã đóng popup hồng bao")
                                break
                        except:
                            continue

                except:
                    pass

                return True
            else:
                print("⚠️  Không tìm thấy hồng bao")
                return False

        except Exception as e:
            print(f"❌ Lỗi xử lý hồng bao: {e}")
            return False

    def register_account(self, username, password, fullname):
        """Đăng ký tài khoản"""
        try:
            print(f"\n🚀 ĐĂNG KÝ: {username}")
            print("="*40)

            # Đi đến trang đăng ký
            register_url = "https://www.13win16.com/home/<USER>"
            self.driver.get(register_url)
            time.sleep(8)

            # Chụp màn hình trang đăng ký
            self.save_screenshot(f"01_register_page_{username}.png")

            # Tìm tất cả input text
            inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
            print(f"📝 Tìm thấy {len(inputs)} ô input")

            if len(inputs) < 3:
                print("❌ Không đủ ô input")
                return False

            # Điền form theo thứ tự
            try:
                # Ô 1: Username
                inputs[0].clear()
                inputs[0].send_keys(username)
                print(f"✅ Username: {username}")
                time.sleep(1)

                # Ô 2: Password
                inputs[1].clear()
                inputs[1].send_keys(password)
                print(f"✅ Password: {password}")
                time.sleep(1)

                # Ô 3: Confirm Password
                if len(inputs) >= 3:
                    inputs[2].clear()
                    inputs[2].send_keys(password)
                    print("✅ Confirm Password")
                    time.sleep(1)

                # Ô 4: Fullname
                if len(inputs) >= 4:
                    inputs[3].clear()
                    inputs[3].send_keys(fullname)
                    print(f"✅ Fullname: {fullname}")
                    time.sleep(1)

            except Exception as e:
                print(f"❌ Lỗi điền form: {e}")
                return False

            # Chụp màn hình sau khi điền form
            self.save_screenshot(f"02_filled_form_{username}.png")

            # Checkbox
            try:
                checkbox = self.driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Checkbox")
            except:
                print("⚠️  Không có checkbox")

            # Submit
            try:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    submit_btn = buttons[-1]  # Button cuối cùng
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_btn)
                    time.sleep(1)
                    submit_btn.click()
                    print("🚀 Đã submit")
                    time.sleep(8)
                else:
                    print("❌ Không tìm thấy nút submit")
                    return False
            except Exception as e:
                print(f"❌ Lỗi submit: {e}")
                return False

            # Chụp màn hình kết quả đăng ký
            self.save_screenshot(f"03_register_result_{username}.png")

            # Kiểm tra kết quả
            page_source = self.driver.page_source.lower()

            if any(keyword in page_source for keyword in ["thành công", "success", "welcome"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")

                # Tìm và click hồng bao (popup thưởng)
                if self.handle_red_envelope(username):
                    print(f"{Fore.GREEN}🧧 Đã nhận hồng bao!{Style.RESET_ALL}")
                else:
                    print(f"{Fore.YELLOW}⚠️  Không tìm thấy hồng bao{Style.RESET_ALL}")

                # Lưu tài khoản
                with open("accounts.txt", "a", encoding="utf-8") as f:
                    f.write(f"{username}|{password}|{fullname}\n")

                return True

            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already"]):
                print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                return False

            else:
                print(f"{Fore.RED}❓ Không rõ kết quả{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"❌ Lỗi đăng ký: {e}")
            return False

    def claim_rewards(self, username, password):
        """Nhận thưởng đơn giản"""
        try:
            print(f"\n🎁 NHẬN THƯỞNG: {username}")
            print("="*40)

            # Đăng nhập
            login_url = "https://www.13win16.com/home/<USER>"
            self.driver.get(login_url)
            time.sleep(5)

            # Điền login
            inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input')
            if len(inputs) >= 2:
                # Username
                inputs[0].clear()
                inputs[0].send_keys(username)
                time.sleep(1)

                # Password
                inputs[1].clear()
                inputs[1].send_keys(password)
                time.sleep(1)

                # Submit login
                buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    buttons[-1].click()
                    print("🔑 Đã đăng nhập")
                    time.sleep(5)

            # Đi đến trang nhiệm vụ
            task_url = "https://www.13win16.com/home/<USER>"
            self.driver.get(task_url)
            time.sleep(8)

            # Chụp màn hình trang nhiệm vụ
            self.save_screenshot(f"06_task_page_{username}.png")

            # Tìm và click nút "Nhận"
            claimed = 0

            # Thử nhiều cách tìm nút Nhận
            selectors = [
                "//button[contains(text(), 'Nhận')]",
                "//div[contains(text(), 'Nhận')]",
                "//span[contains(text(), 'Nhận')]",
                "//a[contains(text(), 'Nhận')]"
            ]

            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        try:
                            if elem.is_displayed():
                                # Scroll đến element
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", elem)
                                time.sleep(2)

                                # Click
                                elem.click()
                                claimed += 1
                                print(f"✅ Đã nhận thưởng {claimed}")
                                time.sleep(3)
                        except:
                            continue
                except:
                    continue

            # Chụp màn hình sau khi nhận thưởng
            self.save_screenshot(f"07_after_claim_{username}.png")

            if claimed > 0:
                print(f"{Fore.GREEN}🎁 Đã nhận {claimed} phần thưởng!{Style.RESET_ALL}")
                return True
            else:
                print(f"{Fore.YELLOW}⚠️  Không tìm thấy thưởng hoặc đã nhận hết{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"❌ Lỗi nhận thưởng: {e}")
            return False

    def run_single(self):
        """Chạy đăng ký 1 tài khoản"""
        print("🎯 TOOL ĐĂNG KÝ + NHẬN THƯỞNG 13WIN")
        print("="*50)

        # Nhập thông tin
        username = input("👤 Username: ").strip()
        password = input("🔒 Password (để trống = tự động): ").strip()
        fullname = input("👨 Họ tên (mặc định: TRAN HOANG AN): ").strip() or "TRAN HOANG AN"

        if not username:
            print("❌ Phải nhập username!")
            return

        if not password:
            password = self.generate_password()
            print(f"🔒 Password tự động: {password}")

        # Khởi tạo
        if not self.setup_driver():
            return

        try:
            # Đăng ký
            if self.register_account(username, password, fullname):
                print(f"{Fore.GREEN}✅ ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")

                # Hỏi nhận thưởng
                choice = input(f"\n{Fore.YELLOW}Nhận thưởng luôn? (y/n): {Style.RESET_ALL}").lower()

                if choice == 'y':
                    if self.claim_rewards(username, password):
                        print(f"{Fore.GREEN}🎉 HOÀN THÀNH!{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.YELLOW}⚠️  Nhận thưởng không thành công{Style.RESET_ALL}")

            else:
                print(f"{Fore.RED}❌ ĐĂNG KÝ THẤT BẠI!{Style.RESET_ALL}")

        finally:
            if self.driver:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng...{Style.RESET_ALL}")
                self.driver.quit()

    def register_in_new_tab(self, username, password, fullname):
        """Đăng ký trong tab mới"""
        try:
            # Mở tab mới
            self.driver.execute_script("window.open('');")
            tabs = self.driver.window_handles
            self.driver.switch_to.window(tabs[-1])  # Chuyển đến tab mới

            print(f"📑 Mở tab mới cho: {username}")

            # Đăng ký trong tab này
            result = self.register_account(username, password, fullname)

            return result

        except Exception as e:
            print(f"❌ Lỗi mở tab mới: {e}")
            return False

    def claim_rewards_in_tab(self, username, password, tab_index):
        """Nhận thưởng trong tab cụ thể"""
        try:
            tabs = self.driver.window_handles
            if tab_index < len(tabs):
                self.driver.switch_to.window(tabs[tab_index])
                print(f"📑 Chuyển đến tab {tab_index + 1} cho: {username}")

                return self.claim_rewards(username, password)
            else:
                print(f"❌ Không tìm thấy tab {tab_index + 1}")
                return False

        except Exception as e:
            print(f"❌ Lỗi chuyển tab: {e}")
            return False

    def run_batch(self):
        """Chạy đăng ký hàng loạt với nhiều tab"""
        print("🚀 ĐĂNG KÝ HÀNG LOẠT - NHIỀU TAB")
        print("="*40)

        # Cấu hình
        prefix = input("👤 Prefix username (mặc định: taolatrumnohu): ").strip() or "taolatrumnohu"
        try:
            count = int(input("📝 Số lượng: "))
            start = int(input("📝 Bắt đầu từ số (mặc định: 1): ") or "1")
        except:
            print("❌ Số không hợp lệ!")
            return

        fullname = "TRAN HOANG AN"

        print(f"\n📋 Sẽ đăng ký {count} tài khoản:")
        print(f"👤 {prefix}{start} → {prefix}{start+count-1}")
        print(f"👨 Họ tên: {fullname}")
        print(f"📑 Mỗi tài khoản sẽ đăng ký trong tab riêng")

        if input("Tiếp tục? (y/n): ").lower() != 'y':
            return

        # Khởi tạo
        if not self.setup_driver():
            return

        success_accounts = []  # Lưu tài khoản thành công
        failed_count = 0

        try:
            # GIAI ĐOẠN 1: Đăng ký tất cả tài khoản trong các tab riêng
            print(f"\n🔥 GIAI ĐOẠN 1: ĐĂNG KÝ {count} TÀI KHOẢN")
            print("="*50)

            for i in range(count):
                num = start + i
                username = f"{prefix}{num}"
                password = self.generate_password()

                print(f"\n📊 {i+1}/{count}: {username}")

                if i == 0:
                    # Tab đầu tiên sử dụng tab hiện tại
                    if self.register_account(username, password, fullname):
                        success_accounts.append((username, password, 0))  # tab index 0
                        print(f"{Fore.GREEN}✅ Thành công tab 1: {username}{Style.RESET_ALL}")
                    else:
                        failed_count += 1
                        print(f"{Fore.RED}❌ Thất bại tab 1: {username}{Style.RESET_ALL}")
                else:
                    # Các tab tiếp theo mở tab mới
                    if self.register_in_new_tab(username, password, fullname):
                        success_accounts.append((username, password, i))  # tab index i
                        print(f"{Fore.GREEN}✅ Thành công tab {i+1}: {username}{Style.RESET_ALL}")
                    else:
                        failed_count += 1
                        print(f"{Fore.RED}❌ Thất bại tab {i+1}: {username}{Style.RESET_ALL}")

                # Delay ngắn giữa các lần đăng ký
                if i < count - 1:
                    delay = random.randint(2, 5)
                    print(f"⏳ Chờ {delay}s...")
                    time.sleep(delay)

            # GIAI ĐOẠN 2: Nhận thưởng cho các tài khoản thành công
            if success_accounts:
                print(f"\n🎁 GIAI ĐOẠN 2: NHẬN THƯỞNG CHO {len(success_accounts)} TÀI KHOẢN")
                print("="*60)

                choice = input(f"\n{Fore.YELLOW}Có muốn nhận thưởng cho tất cả tài khoản thành công? (y/n): {Style.RESET_ALL}").lower()

                if choice == 'y':
                    claimed_count = 0

                    for username, password, tab_index in success_accounts:
                        print(f"\n🎁 Nhận thưởng cho: {username}")

                        if self.claim_rewards_in_tab(username, password, tab_index):
                            claimed_count += 1
                            print(f"{Fore.GREEN}✅ Đã nhận thưởng: {username}{Style.RESET_ALL}")
                        else:
                            print(f"{Fore.YELLOW}⚠️  Không thể nhận thưởng: {username}{Style.RESET_ALL}")

                        # Delay giữa các lần nhận thưởng
                        time.sleep(random.randint(2, 4))

                    print(f"\n🎁 Đã nhận thưởng cho {claimed_count}/{len(success_accounts)} tài khoản")

            # Tóm tắt cuối
            print(f"\n📊 KẾT QUẢ CUỐI CÙNG:")
            print(f"✅ Đăng ký thành công: {len(success_accounts)}")
            print(f"❌ Đăng ký thất bại: {failed_count}")
            print(f"📑 Số tab đã mở: {len(self.driver.window_handles)}")
            print(f"📁 Tài khoản lưu trong: accounts.txt")

        except KeyboardInterrupt:
            print(f"\n⚠️  Đã dừng bởi người dùng!")
        finally:
            if self.driver:
                print(f"\n📑 Hiện có {len(self.driver.window_handles)} tab đang mở")
                choice = input(f"{Fore.CYAN}Đóng tất cả tab? (y/n): {Style.RESET_ALL}").lower()

                if choice == 'y':
                    self.driver.quit()
                else:
                    print("🌐 Browser vẫn mở, bạn có thể kiểm tra thủ công")

def main():
    """Main function"""
    try:
        tool = FinalTool()

        print("🎯 TOOL 13WIN - CHỌN CHỨC NĂNG")
        print("="*40)
        print("1. Đăng ký 1 tài khoản + nhận thưởng")
        print("2. Đăng ký hàng loạt")

        choice = input("\nChọn (1/2): ").strip()

        if choice == "1":
            tool.run_single()
        elif choice == "2":
            tool.run_batch()
        else:
            print("❌ Lựa chọn không hợp lệ!")

    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
