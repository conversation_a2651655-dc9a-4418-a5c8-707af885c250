"""
TOOL DUY NHẤT - ĐĂNG KÝ + NHẬN THƯỞNG 13WIN
Tool hoàn chỉnh, đ<PERSON><PERSON>, dễ sử dụng
"""

import time
import random
import string
import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class ChromeManager:
    """Quản lý nhiều Chrome instances"""
    def __init__(self, chromedriver_path):
        self.chromedriver_path = chromedriver_path
        self.drivers = {}  # {username: driver}

    def create_driver(self, username):
        """Tạo Chrome instance mới cho tài khoản"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # Tạo profile riêng cho mỗi tài khoản
            profile_path = f"chrome_profiles/{username}"
            chrome_options.add_argument(f'--user-data-dir={profile_path}')

            service = Service(self.chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(3)

            self.drivers[username] = driver
            print(f"✅ Đã tạo Chrome instance cho: {username}")
            return driver

        except Exception as e:
            print(f"❌ Lỗi tạo Chrome instance cho {username}: {e}")
            return None

    def get_driver(self, username):
        """Lấy driver cho tài khoản"""
        return self.drivers.get(username)

    def close_driver(self, username):
        """Đóng driver cho tài khoản"""
        if username in self.drivers:
            try:
                self.drivers[username].quit()
                del self.drivers[username]
                print(f"✅ Đã đóng Chrome instance cho: {username}")
            except:
                pass

    def close_all_drivers(self):
        """Đóng tất cả drivers"""
        for username in list(self.drivers.keys()):
            self.close_driver(username)

class FinalTool:
    def __init__(self):
        self.chromedriver_path = "drivers/chromedriver.exe"
        self.screenshot_folder = "chụp màn hình"
        self.account_rewards = {}  # Theo dõi thưởng của từng tài khoản
        self.target_reward = 52  # Mục tiêu 52 D/tài khoản
        self.chrome_manager = ChromeManager(self.chromedriver_path)

        # Tạo folder screenshot nếu chưa có
        if not os.path.exists(self.screenshot_folder):
            os.makedirs(self.screenshot_folder)
            print(f"📁 Đã tạo folder: {self.screenshot_folder}")

        # Tạo folder profiles nếu chưa có
        if not os.path.exists("chrome_profiles"):
            os.makedirs("chrome_profiles")
            print(f"📁 Đã tạo folder: chrome_profiles")

    def save_screenshot(self, filename, driver=None):
        """Lưu screenshot vào folder chụp màn hình"""
        try:
            if driver is None:
                print(f"⚠️  Không có driver để chụp màn hình: {filename}")
                return False

            filepath = os.path.join(self.screenshot_folder, filename)
            driver.save_screenshot(filepath)
            print(f"📸 Screenshot: {filename}")
            return True
        except Exception as e:
            print(f"⚠️  Lỗi chụp màn hình: {e}")
            return False

    def setup_driver(self):
        """Khởi tạo Chrome driver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            service = Service(self.chromedriver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(3)

            print("✅ Đã khởi tạo browser")
            return True

        except Exception as e:
            print(f"❌ Lỗi khởi tạo: {e}")
            return False

    def generate_password(self):
        """Tạo mật khẩu ngẫu nhiên"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(8)) + "123"

    def init_account_reward(self, username):
        """Khởi tạo theo dõi thưởng cho tài khoản"""
        self.account_rewards[username] = {
            'register': 13,  # Thưởng đăng ký
            'red_envelope': 0,  # Thưởng hồng bao
            'contact': 4,  # Thưởng cài đặt phương thức liên lạc
            'app_login': 0,  # Thưởng đăng nhập app (35D)
            'total': 17  # Tổng hiện tại
        }

    def update_reward(self, username, reward_type, amount):
        """Cập nhật thưởng cho tài khoản"""
        if username in self.account_rewards:
            self.account_rewards[username][reward_type] = amount
            # Tính lại tổng
            rewards = self.account_rewards[username]
            rewards['total'] = rewards['register'] + rewards['red_envelope'] + rewards['contact'] + rewards['app_login']

            print(f"💰 {username}: {rewards['total']}/{self.target_reward} D")
            return rewards['total']
        return 0

    def check_account_complete(self, username):
        """Kiểm tra tài khoản đã đạt mục tiêu chưa"""
        if username in self.account_rewards:
            return self.account_rewards[username]['total'] >= self.target_reward
        return False

    def get_incomplete_accounts(self):
        """Lấy danh sách tài khoản chưa đạt mục tiêu"""
        incomplete = []
        for username, rewards in self.account_rewards.items():
            if rewards['total'] < self.target_reward:
                incomplete.append((username, rewards['total']))
        return incomplete

    def display_reward_summary(self):
        """Hiển thị tóm tắt thưởng của tất cả tài khoản"""
        print(f"\n💰 TỔNG KẾT THƯỞNG:")
        print("="*50)

        for username, rewards in self.account_rewards.items():
            status = "✅" if rewards['total'] >= self.target_reward else "⏳"
            print(f"{status} {username}: {rewards['total']}/{self.target_reward} D")

            if rewards['total'] < self.target_reward:
                missing = self.target_reward - rewards['total']
                print(f"   📝 Còn thiếu: {missing} D")

                if rewards['app_login'] == 0:
                    print(f"   📱 Cần đăng nhập app: +35 D")

        complete_count = sum(1 for r in self.account_rewards.values() if r['total'] >= self.target_reward)
        total_count = len(self.account_rewards)

        print(f"\n📊 Hoàn thành: {complete_count}/{total_count} tài khoản")

        return complete_count == total_count

    def handle_notifications_and_rewards(self, username):
        """Xử lý thông báo và chuyển đến trang nhận thưởng"""
        try:
            print("🔔 Xử lý thông báo sau đăng ký...")

            # Chờ thông báo xuất hiện
            time.sleep(5)

            # Chụp màn hình trước khi xử lý
            self.save_screenshot(f"04_after_register_{username}.png")

            # Đóng tất cả popup/thông báo có thể xuất hiện
            notification_close_attempts = 0
            max_attempts = 10

            while notification_close_attempts < max_attempts:
                closed_something = False

                # Các selector để đóng popup/thông báo
                close_selectors = [
                    # Nút đóng phổ biến
                    "//button[contains(text(), 'OK')]",
                    "//button[contains(text(), 'Đóng')]",
                    "//button[contains(text(), 'Close')]",
                    "//button[contains(text(), 'Xác nhận')]",
                    "//button[contains(text(), 'Tiếp tục')]",
                    "//span[contains(text(), 'OK')]",
                    "//span[contains(text(), 'Đóng')]",
                    "//div[contains(text(), 'OK')]",

                    # Icon đóng
                    ".close",
                    ".close-btn",
                    ".modal-close",
                    ".popup-close",
                    ".btn-close",
                    "[aria-label='Close']",
                    "[title='Close']",

                    # X button
                    "//button[text()='×']",
                    "//span[text()='×']",
                    "//div[text()='×']",

                    # Modal overlay (click để đóng)
                    ".modal-backdrop",
                    ".overlay",

                    # Nút trong modal
                    ".modal button",
                    ".popup button",
                    ".dialog button",
                    ".alert button"
                ]

                for selector in close_selectors:
                    try:
                        if selector.startswith('//'):
                            elements = self.driver.find_elements(By.XPATH, selector)
                        else:
                            elements = self.driver.find_elements(By.CSS_SELECTOR, selector)

                        for element in elements:
                            try:
                                if element.is_displayed() and element.is_enabled():
                                    # Scroll đến element
                                    self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                    time.sleep(0.5)

                                    # Click để đóng
                                    element.click()
                                    print(f"✅ Đã đóng thông báo: {selector}")
                                    closed_something = True
                                    time.sleep(1)
                                    break
                            except:
                                continue

                        if closed_something:
                            break

                    except:
                        continue

                # Thử nhấn ESC để đóng popup
                if not closed_something:
                    try:
                        from selenium.webdriver.common.keys import Keys
                        self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                        print("✅ Đã nhấn ESC để đóng popup")
                        closed_something = True
                        time.sleep(1)
                    except:
                        pass

                if not closed_something:
                    break

                notification_close_attempts += 1

            # Chụp màn hình sau khi đóng thông báo
            self.save_screenshot(f"05_notifications_closed_{username}.png")

            # Chuyển đến trang nhận thưởng
            print("🎁 Chuyển đến trang nhận thưởng...")

            # Thử các cách để đến trang nhận thưởng
            reward_navigation_success = False

            # Cách 1: Click vào menu/link nhận thưởng
            reward_links = [
                "//a[contains(text(), 'Khuyến mãi')]",
                "//a[contains(text(), 'Thưởng')]",
                "//a[contains(text(), 'Reward')]",
                "//a[contains(text(), 'Bonus')]",
                "//div[contains(text(), 'Khuyến mãi')]",
                "//div[contains(text(), 'Thưởng')]",
                ".reward-menu",
                ".promotion-menu",
                "[href*='promotion']",
                "[href*='reward']",
                "[href*='bonus']"
            ]

            for selector in reward_links:
                try:
                    if selector.startswith('//'):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)

                    if element.is_displayed():
                        element.click()
                        print(f"✅ Đã click vào menu thưởng: {selector}")
                        reward_navigation_success = True
                        time.sleep(3)
                        break
                except:
                    continue

            # Cách 2: Điều hướng trực tiếp đến URL thưởng
            if not reward_navigation_success:
                try:
                    reward_urls = [
                        "https://www.13win16.com/home/<USER>",
                        "https://www.13win16.com/home/<USER>",
                        "https://www.13win16.com/home/<USER>"
                    ]

                    for url in reward_urls:
                        try:
                            self.driver.get(url)
                            print(f"✅ Đã điều hướng đến: {url}")
                            time.sleep(3)
                            reward_navigation_success = True
                            break
                        except:
                            continue

                except:
                    pass

            # Chụp màn hình trang thưởng
            if reward_navigation_success:
                self.save_screenshot(f"06_reward_page_{username}.png")
                print("✅ Đã chuyển đến trang nhận thưởng")
                return True
            else:
                print("⚠️  Không thể chuyển đến trang thưởng")
                return False

        except Exception as e:
            print(f"❌ Lỗi xử lý thông báo: {e}")
            return False

    def register_account(self, username, password, fullname):
        """Đăng ký tài khoản với Chrome instance riêng"""
        try:
            print(f"\n🚀 ĐĂNG KÝ: {username}")
            print("="*40)

            # Tạo Chrome instance riêng cho tài khoản này
            driver = self.chrome_manager.create_driver(username)
            if not driver:
                return False

            # Đi đến trang đăng ký
            register_url = "https://www.13win16.com/home/<USER>"
            driver.get(register_url)
            time.sleep(8)

            # Chụp màn hình trang đăng ký
            self.save_screenshot(f"01_register_page_{username}.png", driver)

            # Tìm tất cả input text
            inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
            print(f"📝 Tìm thấy {len(inputs)} ô input")

            if len(inputs) < 3:
                print("❌ Không đủ ô input")
                return False

            # Điền form theo thứ tự
            try:
                # Ô 1: Username
                inputs[0].clear()
                inputs[0].send_keys(username)
                print(f"✅ Username: {username}")
                time.sleep(1)

                # Ô 2: Password
                inputs[1].clear()
                inputs[1].send_keys(password)
                print(f"✅ Password: {password}")
                time.sleep(1)

                # Ô 3: Confirm Password
                if len(inputs) >= 3:
                    inputs[2].clear()
                    inputs[2].send_keys(password)
                    print("✅ Confirm Password")
                    time.sleep(1)

                # Ô 4: Fullname
                if len(inputs) >= 4:
                    inputs[3].clear()
                    inputs[3].send_keys(fullname)
                    print(f"✅ Fullname: {fullname}")
                    time.sleep(1)

            except Exception as e:
                print(f"❌ Lỗi điền form: {e}")
                return False

            # Chụp màn hình sau khi điền form
            self.save_screenshot(f"02_filled_form_{username}.png")

            # Checkbox
            try:
                checkbox = self.driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Checkbox")
            except:
                print("⚠️  Không có checkbox")

            # Submit
            try:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    submit_btn = buttons[-1]  # Button cuối cùng
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_btn)
                    time.sleep(1)
                    submit_btn.click()
                    print("🚀 Đã submit")
                    time.sleep(8)
                else:
                    print("❌ Không tìm thấy nút submit")
                    return False
            except Exception as e:
                print(f"❌ Lỗi submit: {e}")
                return False

            # Chụp màn hình kết quả đăng ký
            self.save_screenshot(f"03_register_result_{username}.png")

            # Kiểm tra kết quả
            page_source = self.driver.page_source.lower()

            if any(keyword in page_source for keyword in ["thành công", "success", "welcome"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")

                # Khởi tạo theo dõi thưởng
                self.init_account_reward(username)

                # Xử lý thông báo và chuyển đến trang thưởng
                if self.handle_notifications_and_rewards(username):
                    print(f"{Fore.GREEN}🎁 Đã xử lý thông báo và chuyển đến trang thưởng!{Style.RESET_ALL}")
                    # Cập nhật thưởng hồng bao (ước tính)
                    self.update_reward(username, 'red_envelope', 5)  # Ước tính 5D
                else:
                    print(f"{Fore.YELLOW}⚠️  Có lỗi xử lý thông báo{Style.RESET_ALL}")

                # Lưu tài khoản
                with open("accounts.txt", "a", encoding="utf-8") as f:
                    f.write(f"{username}|{password}|{fullname}\n")

                return True

            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already"]):
                print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                return False

            else:
                print(f"{Fore.RED}❓ Không rõ kết quả{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"❌ Lỗi đăng ký: {e}")
            return False

    def claim_rewards(self, username, password):
        """Nhận thưởng đơn giản"""
        try:
            print(f"\n🎁 NHẬN THƯỞNG: {username}")
            print("="*40)

            # Đăng nhập
            login_url = "https://www.13win16.com/home/<USER>"
            self.driver.get(login_url)
            time.sleep(5)

            # Điền login
            inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input')
            if len(inputs) >= 2:
                # Username
                inputs[0].clear()
                inputs[0].send_keys(username)
                time.sleep(1)

                # Password
                inputs[1].clear()
                inputs[1].send_keys(password)
                time.sleep(1)

                # Submit login
                buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    buttons[-1].click()
                    print("🔑 Đã đăng nhập")
                    time.sleep(5)

            # Đi đến trang nhiệm vụ
            task_url = "https://www.13win16.com/home/<USER>"
            self.driver.get(task_url)
            time.sleep(8)

            # Chụp màn hình trang nhiệm vụ
            self.save_screenshot(f"06_task_page_{username}.png")

            # Tìm và click nút "Nhận"
            claimed = 0

            # Thử nhiều cách tìm nút Nhận
            selectors = [
                "//button[contains(text(), 'Nhận')]",
                "//div[contains(text(), 'Nhận')]",
                "//span[contains(text(), 'Nhận')]",
                "//a[contains(text(), 'Nhận')]"
            ]

            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        try:
                            if elem.is_displayed():
                                # Scroll đến element
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", elem)
                                time.sleep(2)

                                # Click
                                elem.click()
                                claimed += 1
                                print(f"✅ Đã nhận thưởng {claimed}")
                                time.sleep(3)
                        except:
                            continue
                except:
                    continue

            # Chụp màn hình sau khi nhận thưởng
            self.save_screenshot(f"07_after_claim_{username}.png")

            if claimed > 0:
                print(f"{Fore.GREEN}🎁 Đã nhận {claimed} phần thưởng!{Style.RESET_ALL}")

                # Cập nhật thưởng app nếu có
                if username in self.account_rewards:
                    self.update_reward(username, 'app_login', 35)  # Thưởng app 35D

                return True
            else:
                print(f"{Fore.YELLOW}⚠️  Không tìm thấy thưởng hoặc đã nhận hết{Style.RESET_ALL}")
                return False

        except Exception as e:
            print(f"❌ Lỗi nhận thưởng: {e}")
            return False

    def run_single(self):
        """Chạy đăng ký 1 tài khoản"""
        print("🎯 TOOL ĐĂNG KÝ + NHẬN THƯỞNG 13WIN")
        print("="*50)

        # Nhập thông tin
        username = input("👤 Username: ").strip()
        password = input("🔒 Password (để trống = tự động): ").strip()
        fullname = input("👨 Họ tên (mặc định: TRAN HOANG AN): ").strip() or "TRAN HOANG AN"

        if not username:
            print("❌ Phải nhập username!")
            return

        if not password:
            password = self.generate_password()
            print(f"🔒 Password tự động: {password}")

        # Khởi tạo
        if not self.setup_driver():
            return

        try:
            # Đăng ký
            if self.register_account(username, password, fullname):
                print(f"{Fore.GREEN}✅ ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")

                # Hiển thị thưởng hiện tại
                self.display_reward_summary()

                # Hỏi nhận thưởng
                choice = input(f"\n{Fore.YELLOW}Nhận thưởng luôn? (y/n): {Style.RESET_ALL}").lower()

                if choice == 'y':
                    if self.claim_rewards(username, password):
                        print(f"{Fore.GREEN}🎉 ĐÃ NHẬN THƯỞNG!{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.YELLOW}⚠️  Nhận thưởng không thành công{Style.RESET_ALL}")

                # Kiểm tra đã đạt mục tiêu chưa
                self.display_reward_summary()

                if self.check_account_complete(username):
                    print(f"\n{Fore.GREEN}🎉 TÀI KHOẢN ĐÃ ĐẠT MỤC TIÊU 52D!{Style.RESET_ALL}")
                else:
                    incomplete = self.get_incomplete_accounts()
                    if incomplete:
                        print(f"\n{Fore.YELLOW}📱 HƯỚNG DẪN NHẬN THÊM THƯỞNG:{Style.RESET_ALL}")
                        print("1. Mở app 13win trên điện thoại/BlueStacks")
                        print("2. Đăng nhập với tài khoản vừa tạo")
                        print("3. Quay lại tool để nhận thưởng app (+35D)")

                        app_choice = input(f"\n{Fore.CYAN}Đã đăng nhập app? Nhận thưởng ngay? (y/n): {Style.RESET_ALL}").lower()

                        if app_choice == 'y':
                            if self.claim_rewards(username, password):
                                self.display_reward_summary()

            else:
                print(f"{Fore.RED}❌ ĐĂNG KÝ THẤT BẠI!{Style.RESET_ALL}")

        finally:
            if self.driver:
                # Kiểm tra tất cả tài khoản đã đạt mục tiêu chưa
                all_complete = self.display_reward_summary()

                if all_complete:
                    print(f"\n{Fore.GREEN}🎉 TẤT CẢ TÀI KHOẢN ĐÃ ĐẠT 52D!{Style.RESET_ALL}")
                    input(f"{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                    self.driver.quit()
                else:
                    incomplete = self.get_incomplete_accounts()
                    print(f"\n{Fore.YELLOW}⚠️  CÒN {len(incomplete)} TÀI KHOẢN CHƯA ĐẠT MỤC TIÊU{Style.RESET_ALL}")

                    choice = input(f"{Fore.CYAN}Đóng browser? (y/n): {Style.RESET_ALL}").lower()
                    if choice == 'y':
                        self.driver.quit()
                    else:
                        print("🌐 Browser vẫn mở để bạn hoàn thành thủ công")

    def register_in_new_tab(self, username, password, fullname):
        """Đăng ký trong tab mới"""
        try:
            # Mở tab mới
            self.driver.execute_script("window.open('');")
            tabs = self.driver.window_handles
            self.driver.switch_to.window(tabs[-1])  # Chuyển đến tab mới

            print(f"📑 Mở tab mới cho: {username}")

            # Đăng ký trong tab này
            result = self.register_account(username, password, fullname)

            return result

        except Exception as e:
            print(f"❌ Lỗi mở tab mới: {e}")
            return False

    def claim_rewards_in_tab(self, username, password, tab_index):
        """Nhận thưởng trong tab cụ thể"""
        try:
            tabs = self.driver.window_handles
            if tab_index < len(tabs):
                self.driver.switch_to.window(tabs[tab_index])
                print(f"📑 Chuyển đến tab {tab_index + 1} cho: {username}")

                return self.claim_rewards(username, password)
            else:
                print(f"❌ Không tìm thấy tab {tab_index + 1}")
                return False

        except Exception as e:
            print(f"❌ Lỗi chuyển tab: {e}")
            return False

    def run_batch(self):
        """Chạy đăng ký hàng loạt với nhiều Chrome instances"""
        print("🚀 ĐĂNG KÝ HÀNG LOẠT - NHIỀU CHROME INSTANCES")
        print("="*50)

        # Cấu hình
        prefix = input("👤 Prefix username (mặc định: taolatrumnohu): ").strip() or "taolatrumnohu"
        try:
            count = int(input("📝 Số lượng: "))
            start = int(input("📝 Bắt đầu từ số (mặc định: 1): ") or "1")
        except:
            print("❌ Số không hợp lệ!")
            return

        fullname = "TRAN HOANG AN"

        print(f"\n📋 Sẽ đăng ký {count} tài khoản:")
        print(f"👤 {prefix}{start} → {prefix}{start+count-1}")
        print(f"👨 Họ tên: {fullname}")
        print(f"📑 Mỗi tài khoản sẽ đăng ký trong tab riêng")

        if input("Tiếp tục? (y/n): ").lower() != 'y':
            return

        # Khởi tạo
        if not self.setup_driver():
            return

        success_accounts = []  # Lưu tài khoản thành công
        failed_count = 0

        try:
            # GIAI ĐOẠN 1: Đăng ký tất cả tài khoản trong các tab riêng
            print(f"\n🔥 GIAI ĐOẠN 1: ĐĂNG KÝ {count} TÀI KHOẢN")
            print("="*50)

            for i in range(count):
                num = start + i
                username = f"{prefix}{num}"
                password = self.generate_password()

                print(f"\n📊 {i+1}/{count}: {username}")

                if i == 0:
                    # Tab đầu tiên sử dụng tab hiện tại
                    if self.register_account(username, password, fullname):
                        success_accounts.append((username, password, 0))  # tab index 0
                        print(f"{Fore.GREEN}✅ Thành công tab 1: {username}{Style.RESET_ALL}")
                    else:
                        failed_count += 1
                        print(f"{Fore.RED}❌ Thất bại tab 1: {username}{Style.RESET_ALL}")
                else:
                    # Các tab tiếp theo mở tab mới
                    if self.register_in_new_tab(username, password, fullname):
                        success_accounts.append((username, password, i))  # tab index i
                        print(f"{Fore.GREEN}✅ Thành công tab {i+1}: {username}{Style.RESET_ALL}")
                    else:
                        failed_count += 1
                        print(f"{Fore.RED}❌ Thất bại tab {i+1}: {username}{Style.RESET_ALL}")

                # Delay ngắn giữa các lần đăng ký
                if i < count - 1:
                    delay = random.randint(2, 5)
                    print(f"⏳ Chờ {delay}s...")
                    time.sleep(delay)

            # GIAI ĐOẠN 2: Nhận thưởng cho các tài khoản thành công
            if success_accounts:
                print(f"\n🎁 GIAI ĐOẠN 2: NHẬN THƯỞNG CHO {len(success_accounts)} TÀI KHOẢN")
                print("="*60)

                choice = input(f"\n{Fore.YELLOW}Có muốn nhận thưởng cho tất cả tài khoản thành công? (y/n): {Style.RESET_ALL}").lower()

                if choice == 'y':
                    claimed_count = 0

                    for username, password, tab_index in success_accounts:
                        print(f"\n🎁 Nhận thưởng cho: {username}")

                        if self.claim_rewards_in_tab(username, password, tab_index):
                            claimed_count += 1
                            print(f"{Fore.GREEN}✅ Đã nhận thưởng: {username}{Style.RESET_ALL}")
                        else:
                            print(f"{Fore.YELLOW}⚠️  Không thể nhận thưởng: {username}{Style.RESET_ALL}")

                        # Delay giữa các lần nhận thưởng
                        time.sleep(random.randint(2, 4))

                    print(f"\n🎁 Đã nhận thưởng cho {claimed_count}/{len(success_accounts)} tài khoản")

            # Tóm tắt cuối
            print(f"\n📊 KẾT QUẢ CUỐI CÙNG:")
            print(f"✅ Đăng ký thành công: {len(success_accounts)}")
            print(f"❌ Đăng ký thất bại: {failed_count}")
            print(f"📑 Số tab đã mở: {len(self.driver.window_handles)}")
            print(f"📁 Tài khoản lưu trong: accounts.txt")

            # Hiển thị tình trạng thưởng
            if success_accounts:
                self.display_reward_summary()

                incomplete = self.get_incomplete_accounts()
                if incomplete:
                    print(f"\n{Fore.YELLOW}📱 HƯỚNG DẪN NHẬN THÊM THƯỞNG CHO {len(incomplete)} TÀI KHOẢN:{Style.RESET_ALL}")
                    print("1. Mở app 13win trên điện thoại/BlueStacks")
                    print("2. Đăng nhập từng tài khoản")
                    print("3. Quay lại tool để nhận thưởng app (+35D)")

                    for username, current_reward in incomplete:
                        missing = self.target_reward - current_reward
                        print(f"   📝 {username}: Còn thiếu {missing}D")

        except KeyboardInterrupt:
            print(f"\n⚠️  Đã dừng bởi người dùng!")
        finally:
            if self.driver:
                print(f"\n📑 Hiện có {len(self.driver.window_handles)} tab đang mở")

                # Kiểm tra tất cả tài khoản đã đạt mục tiêu chưa
                all_complete = len(self.account_rewards) > 0 and self.display_reward_summary()

                if all_complete:
                    print(f"\n{Fore.GREEN}🎉 TẤT CẢ TÀI KHOẢN ĐÃ ĐẠT 52D!{Style.RESET_ALL}")
                    input(f"{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                    self.driver.quit()
                else:
                    incomplete = self.get_incomplete_accounts()
                    if incomplete:
                        print(f"\n{Fore.YELLOW}⚠️  CÒN {len(incomplete)} TÀI KHOẢN CHƯA ĐẠT MỤC TIÊU 52D{Style.RESET_ALL}")
                        print("📱 Cần đăng nhập app để nhận thêm 35D/tài khoản")

                    choice = input(f"{Fore.CYAN}Đóng browser? (y/n): {Style.RESET_ALL}").lower()

                    if choice == 'y':
                        self.driver.quit()
                    else:
                        print("🌐 Browser vẫn mở để bạn hoàn thành thủ công")

def main():
    """Main function"""
    try:
        tool = FinalTool()

        print("🎯 TOOL 13WIN - CHỌN CHỨC NĂNG")
        print("="*40)
        print("1. Đăng ký 1 tài khoản + nhận thưởng")
        print("2. Đăng ký hàng loạt")

        choice = input("\nChọn (1/2): ").strip()

        if choice == "1":
            tool.run_single()
        elif choice == "2":
            tool.run_batch()
        else:
            print("❌ Lựa chọn không hợp lệ!")

    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
