"""
TOOL DUY NHẤT - ĐĂNG KÝ + NHẬN THƯỞNG 13WI<PERSON> ho<PERSON> chỉnh, đ<PERSON><PERSON>, dễ sử dụng
"""

import time
import random
import string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class FinalTool:
    def __init__(self):
        self.driver = None
        self.chromedriver_path = "drivers/chromedriver.exe"
        
    def setup_driver(self):
        """Khởi tạo Chrome driver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            service = Service(self.chromedriver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(3)
            
            print("✅ Đã khởi tạo browser")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khởi tạo: {e}")
            return False
    
    def generate_password(self):
        """Tạo mật khẩu ngẫu nhiên"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(8)) + "123"
    
    def register_account(self, username, password, fullname):
        """Đăng ký tài khoản"""
        try:
            print(f"\n🚀 ĐĂNG KÝ: {username}")
            print("="*40)
            
            # Đi đến trang đăng ký
            register_url = "https://www.13win16.com/home/<USER>"
            self.driver.get(register_url)
            time.sleep(8)
            
            # Tìm tất cả input text
            inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
            print(f"📝 Tìm thấy {len(inputs)} ô input")
            
            if len(inputs) < 3:
                print("❌ Không đủ ô input")
                return False
            
            # Điền form theo thứ tự
            try:
                # Ô 1: Username
                inputs[0].clear()
                inputs[0].send_keys(username)
                print(f"✅ Username: {username}")
                time.sleep(1)
                
                # Ô 2: Password
                inputs[1].clear()
                inputs[1].send_keys(password)
                print(f"✅ Password: {password}")
                time.sleep(1)
                
                # Ô 3: Confirm Password
                if len(inputs) >= 3:
                    inputs[2].clear()
                    inputs[2].send_keys(password)
                    print("✅ Confirm Password")
                    time.sleep(1)
                
                # Ô 4: Fullname
                if len(inputs) >= 4:
                    inputs[3].clear()
                    inputs[3].send_keys(fullname)
                    print(f"✅ Fullname: {fullname}")
                    time.sleep(1)
                
            except Exception as e:
                print(f"❌ Lỗi điền form: {e}")
                return False
            
            # Checkbox
            try:
                checkbox = self.driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Checkbox")
            except:
                print("⚠️  Không có checkbox")
            
            # Submit
            try:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    submit_btn = buttons[-1]  # Button cuối cùng
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_btn)
                    time.sleep(1)
                    submit_btn.click()
                    print("🚀 Đã submit")
                    time.sleep(8)
                else:
                    print("❌ Không tìm thấy nút submit")
                    return False
            except Exception as e:
                print(f"❌ Lỗi submit: {e}")
                return False
            
            # Kiểm tra kết quả
            page_source = self.driver.page_source.lower()
            
            if any(keyword in page_source for keyword in ["thành công", "success", "welcome"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
                
                # Lưu tài khoản
                with open("accounts.txt", "a", encoding="utf-8") as f:
                    f.write(f"{username}|{password}|{fullname}\n")
                
                return True
                
            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already"]):
                print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                return False
                
            else:
                print(f"{Fore.RED}❓ Không rõ kết quả{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi đăng ký: {e}")
            return False
    
    def claim_rewards(self, username, password):
        """Nhận thưởng đơn giản"""
        try:
            print(f"\n🎁 NHẬN THƯỞNG: {username}")
            print("="*40)
            
            # Đăng nhập
            login_url = "https://www.13win16.com/home/<USER>"
            self.driver.get(login_url)
            time.sleep(5)
            
            # Điền login
            inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input')
            if len(inputs) >= 2:
                # Username
                inputs[0].clear()
                inputs[0].send_keys(username)
                time.sleep(1)
                
                # Password
                inputs[1].clear()
                inputs[1].send_keys(password)
                time.sleep(1)
                
                # Submit login
                buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    buttons[-1].click()
                    print("🔑 Đã đăng nhập")
                    time.sleep(5)
            
            # Đi đến trang nhiệm vụ
            task_url = "https://www.13win16.com/home/<USER>"
            self.driver.get(task_url)
            time.sleep(8)
            
            # Tìm và click nút "Nhận"
            claimed = 0
            
            # Thử nhiều cách tìm nút Nhận
            selectors = [
                "//button[contains(text(), 'Nhận')]",
                "//div[contains(text(), 'Nhận')]",
                "//span[contains(text(), 'Nhận')]",
                "//a[contains(text(), 'Nhận')]"
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        try:
                            if elem.is_displayed():
                                # Scroll đến element
                                self.driver.execute_script("arguments[0].scrollIntoView(true);", elem)
                                time.sleep(2)
                                
                                # Click
                                elem.click()
                                claimed += 1
                                print(f"✅ Đã nhận thưởng {claimed}")
                                time.sleep(3)
                        except:
                            continue
                except:
                    continue
            
            if claimed > 0:
                print(f"{Fore.GREEN}🎁 Đã nhận {claimed} phần thưởng!{Style.RESET_ALL}")
                return True
            else:
                print(f"{Fore.YELLOW}⚠️  Không tìm thấy thưởng hoặc đã nhận hết{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi nhận thưởng: {e}")
            return False
    
    def run_single(self):
        """Chạy đăng ký 1 tài khoản"""
        print("🎯 TOOL ĐĂNG KÝ + NHẬN THƯỞNG 13WIN")
        print("="*50)
        
        # Nhập thông tin
        username = input("👤 Username: ").strip()
        password = input("🔒 Password (để trống = tự động): ").strip()
        fullname = input("👨 Họ tên (mặc định: TRAN HOANG AN): ").strip() or "TRAN HOANG AN"
        
        if not username:
            print("❌ Phải nhập username!")
            return
        
        if not password:
            password = self.generate_password()
            print(f"🔒 Password tự động: {password}")
        
        # Khởi tạo
        if not self.setup_driver():
            return
        
        try:
            # Đăng ký
            if self.register_account(username, password, fullname):
                print(f"{Fore.GREEN}✅ ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
                
                # Hỏi nhận thưởng
                choice = input(f"\n{Fore.YELLOW}Nhận thưởng luôn? (y/n): {Style.RESET_ALL}").lower()
                
                if choice == 'y':
                    if self.claim_rewards(username, password):
                        print(f"{Fore.GREEN}🎉 HOÀN THÀNH!{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.YELLOW}⚠️  Nhận thưởng không thành công{Style.RESET_ALL}")
                
            else:
                print(f"{Fore.RED}❌ ĐĂNG KÝ THẤT BẠI!{Style.RESET_ALL}")
                
        finally:
            if self.driver:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng...{Style.RESET_ALL}")
                self.driver.quit()
    
    def run_batch(self):
        """Chạy đăng ký hàng loạt"""
        print("🚀 ĐĂNG KÝ HÀNG LOẠT")
        print("="*30)
        
        # Cấu hình
        prefix = input("👤 Prefix username (mặc định: taolatrumnohu): ").strip() or "taolatrumnohu"
        try:
            count = int(input("📝 Số lượng: "))
            start = int(input("📝 Bắt đầu từ số (mặc định: 1): ") or "1")
        except:
            print("❌ Số không hợp lệ!")
            return
        
        fullname = "TRAN HOANG AN"
        
        print(f"\n📋 Sẽ đăng ký {count} tài khoản:")
        print(f"👤 {prefix}{start} → {prefix}{start+count-1}")
        print(f"👨 Họ tên: {fullname}")
        
        if input("Tiếp tục? (y/n): ").lower() != 'y':
            return
        
        # Khởi tạo
        if not self.setup_driver():
            return
        
        success = 0
        failed = 0
        
        try:
            for i in range(count):
                num = start + i
                username = f"{prefix}{num}"
                password = self.generate_password()
                
                print(f"\n📊 {i+1}/{count}: {username}")
                
                if self.register_account(username, password, fullname):
                    success += 1
                    print(f"{Fore.GREEN}✅ Thành công: {success}{Style.RESET_ALL}")
                else:
                    failed += 1
                    print(f"{Fore.RED}❌ Thất bại: {failed}{Style.RESET_ALL}")
                
                # Delay
                if i < count - 1:
                    delay = random.randint(3, 8)
                    print(f"⏳ Chờ {delay}s...")
                    time.sleep(delay)
            
            print(f"\n📊 KẾT QUẢ:")
            print(f"✅ Thành công: {success}")
            print(f"❌ Thất bại: {failed}")
            print(f"📁 Tài khoản lưu trong: accounts.txt")
            
        except KeyboardInterrupt:
            print(f"\n⚠️  Đã dừng!")
        finally:
            if self.driver:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng...{Style.RESET_ALL}")
                self.driver.quit()

def main():
    """Main function"""
    try:
        tool = FinalTool()
        
        print("🎯 TOOL 13WIN - CHỌN CHỨC NĂNG")
        print("="*40)
        print("1. Đăng ký 1 tài khoản + nhận thưởng")
        print("2. Đăng ký hàng loạt")
        
        choice = input("\nChọn (1/2): ").strip()
        
        if choice == "1":
            tool.run_single()
        elif choice == "2":
            tool.run_batch()
        else:
            print("❌ Lựa chọn không hợp lệ!")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
