# 📱 HƯỚNG DẪN SETUP BLUESTACKS AUTOMATION

## 🎯 Mục tiêu
Tự động hoàn thành nhiệm vụ **"Tải xuống, đăng nhập"** để nhận thưởng **35.00 D**

## 📋 Yêu cầu
- ✅ BlueStacks đã cài đặt
- ✅ App 13win APK đã cài trong BlueStacks  
- ✅ Python 3.7+
- ✅ ChromeDriver

## 🔧 BƯỚC 1: Cài đặt Dependencies

```bash
python install_dependencies.py
```

Hoặc cài thủ công:
```bash
pip install pyautogui opencv-python pillow selenium colorama
```

## 📸 BƯỚC 2: Chụp Screenshots

Tạo thư mục `images/` và chụp các screenshot sau:

### 1. **13win_icon.png**
- Mở BlueStacks
- Chụp screenshot **icon app 13win** trên màn hình chính
- Crop chỉ phần icon, kích thước nhỏ (~50x50px)

### 2. **username_field.png** 
- Mở app 13win
- Đ<PERSON> đến trang đăng nhập
- Chụp screenshot **ô nhập username/số điện thoại**
- Crop chỉ phần ô input

### 3. **password_field.png**
- Chụp screenshot **ô nhập mật khẩu**
- Crop chỉ phần ô input

### 4. **login_button.png**
- Chụp screenshot **nút đăng nhập**
- Crop chỉ phần nút

### 5. **home_screen.png**
- Chụp screenshot **màn hình chính** sau khi đăng nhập thành công
- Có thể là logo, menu, hoặc phần đặc trưng của trang chủ

## 📁 Cấu trúc thư mục

```
d:\chorme auto\
├── images/
│   ├── 13win_icon.png
│   ├── username_field.png
│   ├── password_field.png
│   ├── login_button.png
│   └── home_screen.png
├── drivers/
│   └── chromedriver.exe
├── bluestack_app_login.py
├── register_batch.py
└── install_dependencies.py
```

## 🚀 BƯỚC 3: Sử dụng

### Cách 1: Tool riêng cho nhiệm vụ app
```bash
python bluestack_app_login.py
```

### Cách 2: Tích hợp trong đăng ký hàng loạt
```bash
python register_batch.py
```
- Sau khi đăng ký thành công
- Tool sẽ hỏi có muốn hoàn thành nhiệm vụ app không
- Chọn `y` để bắt đầu

## 🎯 Quy trình hoạt động

1. **Đăng ký tài khoản** trên web (nếu chưa có)
2. **Khởi động BlueStacks** tự động
3. **Mở app 13win** bằng image recognition
4. **Đăng nhập app** với tài khoản vừa tạo
5. **Quay lại web** để nhận thưởng 35.00 D

## ⚠️ Lưu ý

### Về Screenshots:
- **Chụp chính xác**: Screenshot phải khớp với giao diện hiện tại
- **Kích thước nhỏ**: Chỉ crop phần cần thiết
- **Độ phân giải**: Đảm bảo rõ nét, không bị mờ
- **Màu sắc**: Giữ nguyên màu gốc

### Về BlueStacks:
- **Đường dẫn**: Kiểm tra đường dẫn BlueStacks trong code
- **Phiên bản**: Tool hỗ trợ BlueStacks 5
- **Cài đặt**: App 13win phải đã được cài đặt sẵn

### Về Automation:
- **Tốc độ**: Tool có delay để tránh bị phát hiện
- **Lỗi**: Nếu không tìm thấy element, kiểm tra lại screenshot
- **Debug**: Tool sẽ chụp screenshot khi có lỗi

## 🔧 Troubleshooting

### Lỗi không tìm thấy BlueStacks:
```python
# Sửa đường dẫn trong bluestack_app_login.py
self.bluestacks_path = r"C:\Program Files\BlueStacks_nxt\HD-Player.exe"
```

### Lỗi không tìm thấy image:
- Kiểm tra file screenshot có tồn tại không
- Thử giảm confidence: `confidence=0.7`
- Chụp lại screenshot với kích thước khác

### Lỗi app không mở:
- Kiểm tra app đã cài đặt chưa
- Thử click thủ công để test
- Kiểm tra tên package app

## 📞 Hỗ trợ

Nếu gặp lỗi, hãy:
1. Kiểm tra log chi tiết
2. Chụp screenshot màn hình khi lỗi
3. Kiểm tra file images/ có đầy đủ không
4. Test từng bước thủ công trước

## 🎁 Kết quả mong đợi

Sau khi hoàn thành:
- ✅ Tài khoản đã đăng nhập app thành công
- ✅ Nhiệm vụ "Tải xuống, đăng nhập" hoàn thành
- ✅ Nhận được thưởng 35.00 D trên web
- ✅ Tổng thưởng: 13 + 35 + 4 = 52.00 D
