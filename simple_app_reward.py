"""
Tool đơn giản để nhận thưởng "T<PERSON><PERSON> xuố<PERSON>, đ<PERSON>ng nhập" 
Y<PERSON><PERSON> c<PERSON>u user tự đăng nhập app, sau đó tự động nhận thưởng trên web
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def claim_app_reward_on_web(username, password):
    """Nhận thưởng app trên web sau khi đã đăng nhập app"""
    print(f"\n🌐 NHẬN THƯỞNG 'TẢI XUỐNG, ĐĂNG NHẬP' TRÊN WEB")
    print("="*60)
    print(f"👤 Tài khoản: {username}")
    
    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')
        
        # Sử dụng ChromeDriver local
        chromedriver_path = "drivers/chromedriver.exe"
        service = Service(chromedriver_path)
        
        # Tạo driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(5)
        
        # BƯỚC 1: Đăng nhập web
        print("🔑 BƯỚC 1: Đăng nhập web")
        
        login_url = "https://www.13win16.com/home/<USER>"
        driver.get(login_url)
        time.sleep(3)
        
        # Điền form đăng nhập
        username_field = WebDriverWait(driver, 10).until(
            EC.presence_of_element_located((By.CSS_SELECTOR, 'input[placeholder*="điện thoại"], input[type="text"]'))
        )
        username_field.clear()
        username_field.send_keys(username)
        print(f"✅ Username: {username}")
        
        password_field = driver.find_element(By.CSS_SELECTOR, 'input[type="password"]')
        password_field.clear()
        password_field.send_keys(password)
        print(f"✅ Password: {password}")
        
        login_button = driver.find_element(By.CSS_SELECTOR, 'button[type="submit"], button')
        login_button.click()
        print("🚀 Đã submit đăng nhập...")
        
        time.sleep(5)
        
        # Kiểm tra đăng nhập thành công
        page_source = driver.page_source.lower()
        
        if not any(keyword in page_source for keyword in ["dashboard", "profile", "logout", "đăng xuất", "tài khoản"]):
            print(f"{Fore.RED}❌ ĐĂNG NHẬP WEB THẤT BẠI!{Style.RESET_ALL}")
            return False
        
        print(f"{Fore.GREEN}✅ ĐĂNG NHẬP WEB THÀNH CÔNG!{Style.RESET_ALL}")
        
        # BƯỚC 2: Đi đến trang nhiệm vụ
        print("\n🎁 BƯỚC 2: Nhận thưởng")
        
        task_url = "https://www.13win16.com/home/<USER>"
        driver.get(task_url)
        time.sleep(3)
        
        print(f"📄 URL: {task_url}")
        
        # BƯỚC 3: Tìm và click nút "Nhận"
        print("🔍 Tìm các nút 'Nhận'...")
        
        # Tìm tất cả nút "Nhận"
        claim_buttons = []
        
        try:
            buttons = driver.find_elements(By.XPATH, "//button[contains(text(), 'Nhận')]")
            claim_buttons.extend(buttons)
            print(f"✅ Tìm thấy {len(buttons)} nút 'Nhận'")
        except:
            pass
        
        if not claim_buttons:
            print("⚠️  Không tìm thấy nút 'Nhận' nào")
            
            # Chụp screenshot để debug
            try:
                driver.save_screenshot("no_claim_buttons_debug.png")
                print("📸 Đã chụp screenshot: no_claim_buttons_debug.png")
            except:
                pass
            
            return False
        
        # Click từng nút "Nhận"
        claimed_count = 0
        
        for i, button in enumerate(claim_buttons):
            try:
                if button.is_enabled() and button.is_displayed():
                    # Scroll đến nút
                    driver.execute_script("arguments[0].scrollIntoView(true);", button)
                    time.sleep(1)
                    
                    # Lấy text của nút
                    button_text = button.text
                    print(f"🎯 Click nút {i+1}: '{button_text}'")
                    
                    # Click nút
                    button.click()
                    claimed_count += 1
                    
                    print(f"✅ Đã click nút {i+1}")
                    time.sleep(2)
                    
                else:
                    print(f"⚠️  Nút {i+1} không thể click")
                    
            except Exception as e:
                print(f"❌ Lỗi click nút {i+1}: {e}")
        
        print(f"\n📊 Kết quả:")
        print(f"✅ Đã click: {claimed_count} nút")
        
        # Chụp screenshot kết quả
        try:
            driver.save_screenshot("app_reward_result.png")
            print("📸 Đã chụp screenshot: app_reward_result.png")
        except:
            pass
        
        if claimed_count > 0:
            print(f"{Fore.GREEN}🎁 ĐÃ NHẬN THƯỞNG THÀNH CÔNG!{Style.RESET_ALL}")
            print(f"💰 Có thể đã nhận thưởng 'Tải xuống, đăng nhập' (+35.00 D)")
            return True
        else:
            print(f"{Fore.YELLOW}⚠️  Không thể nhận thưởng{Style.RESET_ALL}")
            return False
        
    except Exception as e:
        print(f"{Fore.RED}❌ Lỗi: {e}{Style.RESET_ALL}")
        return False
        
    finally:
        if driver:
            try:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                driver.quit()
            except:
                pass

def read_accounts_from_file():
    """Đọc tài khoản từ file successful_accounts.txt"""
    accounts = []
    
    try:
        with open("successful_accounts.txt", "r", encoding="utf-8") as f:
            content = f.read()
            
        # Parse tài khoản
        blocks = content.split("-" * 50)
        
        for block in blocks:
            if "Username:" in block and "Password:" in block:
                lines = block.strip().split("\n")
                username = None
                password = None
                
                for line in lines:
                    if line.startswith("Username:"):
                        username = line.split(":", 1)[1].strip()
                    elif line.startswith("Password:"):
                        password = line.split(":", 1)[1].strip()
                
                if username and password:
                    accounts.append((username, password))
        
        return accounts
        
    except FileNotFoundError:
        print("❌ Không tìm thấy file successful_accounts.txt")
        return []
    except Exception as e:
        print(f"❌ Lỗi đọc file: {e}")
        return []

def main():
    """Main function"""
    try:
        print("📱 TOOL NHẬN THƯỞNG 'TẢI XUỐNG, ĐĂNG NHẬP'")
        print("="*60)
        print("🎯 Mục tiêu: Nhận thưởng 35.00 D")
        print("\n📋 Quy trình:")
        print("1. Bạn tự đăng nhập app 13win trên điện thoại/BlueStacks")
        print("2. Tool sẽ tự động nhận thưởng trên web")
        
        # Chọn cách nhập tài khoản
        print(f"\n📝 Chọn cách nhập tài khoản:")
        print("1. Nhập thủ công")
        print("2. Đọc từ file successful_accounts.txt")
        
        choice = input("Chọn (1/2): ").strip()
        
        if choice == "1":
            # Nhập thủ công
            username = input("\n👤 Username: ").strip()
            password = input("🔒 Password: ").strip()
            
            if not username or not password:
                print("❌ Vui lòng nhập đầy đủ thông tin!")
                return
            
            accounts = [(username, password)]
            
        elif choice == "2":
            # Đọc từ file
            accounts = read_accounts_from_file()
            
            if not accounts:
                print("❌ Không có tài khoản nào!")
                return
            
            print(f"\n📊 Tìm thấy {len(accounts)} tài khoản:")
            for i, (username, _) in enumerate(accounts[:5]):
                print(f"  {i+1}. {username}")
            
            if len(accounts) > 5:
                print(f"  ... và {len(accounts) - 5} tài khoản khác")
        
        else:
            print("❌ Lựa chọn không hợp lệ!")
            return
        
        # Xác nhận
        print(f"\n{Fore.YELLOW}📋 HƯỚNG DẪN QUAN TRỌNG:{Style.RESET_ALL}")
        print("1. Mở app 13win trên điện thoại hoặc BlueStacks")
        print("2. Đăng nhập với tài khoản của bạn")
        print("3. Đảm bảo đã đăng nhập thành công")
        print("4. Quay lại đây và nhấn Enter để tiếp tục")
        
        input(f"\n{Fore.CYAN}Nhấn Enter sau khi đã đăng nhập app...{Style.RESET_ALL}")
        
        # Xử lý từng tài khoản
        success_count = 0
        
        for i, (username, password) in enumerate(accounts):
            if len(accounts) > 1:
                print(f"\n📊 Tiến trình: {i+1}/{len(accounts)}")
            
            try:
                if claim_app_reward_on_web(username, password):
                    success_count += 1
                    print(f"{Fore.GREEN}✅ Thành công: {username}{Style.RESET_ALL}")
                else:
                    print(f"{Fore.RED}❌ Thất bại: {username}{Style.RESET_ALL}")
            except Exception as e:
                print(f"{Fore.RED}❌ Lỗi {username}: {e}{Style.RESET_ALL}")
            
            # Delay giữa các tài khoản
            if i < len(accounts) - 1:
                time.sleep(3)
        
        # Tóm tắt
        if len(accounts) > 1:
            print(f"\n📊 KẾT QUẢ CUỐI CÙNG:")
            print(f"✅ Thành công: {success_count}/{len(accounts)}")
            print(f"💰 Ước tính nhận được: {success_count * 35} D")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
