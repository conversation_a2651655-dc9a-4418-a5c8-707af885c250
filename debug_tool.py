"""
Script debug để kiểm tra các bước của tool
"""

import os
import sys
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def check_step_by_step():
    """Kiểm tra từng bước của tool"""
    print("🔍 DEBUG TOOL - KIỂM TRA TỪNG BƯỚC")
    print("="*50)

    # Bước 1: Kiểm tra proxy
    print(f"\n{Fore.YELLOW}Bước 1: Test Proxy Manager{Style.RESET_ALL}")
    try:
        from proxy_manager import ProxyManager
        proxy_manager = ProxyManager()

        print("✅ Import ProxyManager thành công")

        # Test tìm proxy
        print("🔍 Đang test tìm proxy...")
        found_count = proxy_manager.fetch_and_test_proxies()
        print(f"✅ Tìm được {found_count} proxy")

    except Exception as e:
        print(f"❌ Lỗi Proxy Manager: {e}")
        return False

    # Bước 2: Ki<PERSON><PERSON> tra Browser Manager
    print(f"\n{Fore.YELLOW}Bước 2: Test Browser Manager{Style.RESET_ALL}")
    try:
        from browser_manager import BrowserManager
        from config import BROWSER_CONFIG

        print("✅ Import BrowserManager thành công")

        # Test tạo browser
        print("🔍 Đang test tạo browser...")
        BROWSER_CONFIG['use_existing_browser'] = False  # Test tạo browser mới
        browser = BrowserManager()

        if browser.create_browser():
            print("✅ Tạo browser thành công")
            browser.close_browser()
        else:
            print("❌ Không thể tạo browser")
            return False

    except Exception as e:
        print(f"❌ Lỗi Browser Manager: {e}")
        return False

    # Bước 3: Kiểm tra Registration Bot
    print(f"\n{Fore.YELLOW}Bước 3: Test Registration Bot{Style.RESET_ALL}")
    try:
        from registration_bot import RegistrationBot

        print("✅ Import RegistrationBot thành công")

        # Test phân tích form (không thực sự truy cập web)
        print("🔍 Đang test RegistrationBot...")
        bot = RegistrationBot()
        print("✅ Tạo RegistrationBot thành công")

    except Exception as e:
        print(f"❌ Lỗi Registration Bot: {e}")
        return False

    # Bước 4: Kiểm tra Account Generator
    print(f"\n{Fore.YELLOW}Bước 4: Test Account Generator{Style.RESET_ALL}")
    try:
        from account_generator import AccountGenerator

        print("✅ Import AccountGenerator thành công")

        # Test tạo tài khoản
        print("🔍 Đang test tạo tài khoản...")
        generator = AccountGenerator()
        account = generator.generate_complete_account()
        print(f"✅ Tạo tài khoản thành công: {account['username']}")

    except Exception as e:
        print(f"❌ Lỗi Account Generator: {e}")
        return False

    # Bước 5: Kiểm tra Main Tool
    print(f"\n{Fore.YELLOW}Bước 5: Test Main Tool{Style.RESET_ALL}")
    try:
        from main import AutoRegistrationTool

        print("✅ Import AutoRegistrationTool thành công")

        # Test khởi tạo
        print("🔍 Đang test khởi tạo tool...")
        tool = AutoRegistrationTool()
        print("✅ Khởi tạo tool thành công")

    except Exception as e:
        print(f"❌ Lỗi Main Tool: {e}")
        return False

    print(f"\n{Fore.GREEN}✅ TẤT CẢ CÁC BƯỚC ĐỀU HOẠT ĐỘNG BÌNH THƯỜNG{Style.RESET_ALL}")
    return True

def check_config():
    """Kiểm tra cấu hình"""
    print(f"\n{Fore.YELLOW}Kiểm tra cấu hình:{Style.RESET_ALL}")

    try:
        import config

        print(f"✅ URL đăng ký: {config.REGISTER_URL}")
        print(f"✅ Username prefix: {config.ACCOUNT_CONFIG['username_prefix']}")
        print(f"✅ Họ tên cố định: {config.ACCOUNT_CONFIG['fixed_real_name']}")
        print(f"✅ Browser config: {config.BROWSER_CONFIG['headless']}")
        print(f"✅ Proxy sources: {len(config.PROXY_CONFIG['proxy_sources'])} nguồn")

    except Exception as e:
        print(f"❌ Lỗi config: {e}")

def check_files():
    """Kiểm tra files"""
    print(f"\n{Fore.YELLOW}Kiểm tra files:{Style.RESET_ALL}")

    required_files = [
        "main.py",
        "config.py",
        "browser_manager.py",
        "proxy_manager.py",
        "registration_bot.py",
        "account_generator.py"
    ]

    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - THIẾU")

def main():
    """Main function"""
    try:
        check_files()
        check_config()

        print(f"\n{Fore.CYAN}Bắt đầu test từng bước...{Style.RESET_ALL}")
        if check_step_by_step():
            print(f"\n{Fore.GREEN}🎉 DEBUG HOÀN THÀNH - TOOL HOẠT ĐỘNG BÌNH THƯỜNG{Style.RESET_ALL}")
            print(f"\n{Fore.YELLOW}Nếu tool vẫn dừng sau khi tìm proxy, có thể do:{Style.RESET_ALL}")
            print("1. Người dùng không nhấn 'y' để xác nhận")
            print("2. Lỗi khi phân tích form đăng ký")
            print("3. Lỗi kết nối mạng")
            print("4. Website 13win16.com thay đổi cấu trúc")
        else:
            print(f"\n{Fore.RED}❌ CÓ LỖI TRONG QUÁ TRÌNH DEBUG{Style.RESET_ALL}")

    except Exception as e:
        print(f"\n{Fore.RED}❌ Lỗi debug: {e}{Style.RESET_ALL}")

    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
