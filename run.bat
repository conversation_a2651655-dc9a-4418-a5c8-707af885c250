@echo off
echo 🚀 Starting 13Win Account Manager...
echo.

REM Check if executable exists
if not exist "bin\Release\net6.0-windows\win-x64\publish\AccountManager.exe" (
    echo ❌ AccountManager.exe not found!
    echo Please build the project first by running: build.bat
    echo.
    echo 🔨 Building now...
    call build.bat
    if errorlevel 1 (
        echo ❌ Build failed!
        pause
        exit /b 1
    )
)

REM Run the application
echo ✅ Starting Account Manager...
start "" "bin\Release\net6.0-windows\win-x64\publish\AccountManager.exe"

echo 🎉 Account Manager started!
echo.
echo 💡 Tips:
echo - Make sure accounts.txt exists in the same folder
echo - The GUI will auto-load accounts from accounts.txt
echo - Use Tools menu to run the Python registration tool
echo.
timeout /t 3 >nul
