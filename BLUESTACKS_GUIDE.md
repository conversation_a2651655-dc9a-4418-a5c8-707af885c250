# 📱 **HƯỚNG DẪN BLUESTACKS - 13WIN TOOL**

## 🎯 **TỔNG QUAN**

Tool sẽ tự động mở BlueStacks sau mỗi lần đăng ký tài khoản thành công để bạn có thể cài app 13win và nhận thưởng app (+35D).

## ⚙️ **CẤU HÌNH BLUESTACKS**

### 📍 **Đường dẫn mặc định:**
```
C:\Program Files\BlueStacks_nxt\HD-Player.exe
```

### 📱 **Instance sử dụng:**
```
Pie64
```

### 🔧 **Lệnh chạy:**
```bash
"C:\Program Files\BlueStacks_nxt\HD-Player.exe" --instance Pie64
```

## 🚀 **QUY TRÌNH TỰ ĐỘNG**

### ✅ **Khi đăng ký thành công:**
1. **Tool đăng ký tài khoản** → Thành công
2. **Điều hướng đến trang nhiệm vụ** → Chrome sẵn sàng
3. **Tự động mở BlueStacks** → Instance Pie64
4. **Hiển thị hướng dẫn** → Nhận thưởng thủ công

### 📱 **Thông báo BlueStacks:**
```
📱 Mở BlueStacks cho taolatrumnohu1...
✅ Đã mở BlueStacks (instance: Pie64) cho taolatrumnohu1
📱 Process ID: 12345
```

## 📋 **HƯỚNG DẪN SỬ DỤNG BLUESTACKS**

### 🔽 **Bước 1: Tải app 13win**
1. Mở **Google Play Store** trong BlueStacks
2. Tìm kiếm **"13win"**
3. Tải và cài đặt app

### 🔑 **Bước 2: Đăng nhập tài khoản**
1. Mở app 13win
2. Đăng nhập bằng tài khoản vừa đăng ký:
   - **Username:** taolatrumnohu1, taolatrumnohu2, ...
   - **Password:** [mật khẩu bạn đã nhập]
   - **Real Name:** TRAN HOANG AN

### 🎁 **Bước 3: Nhận thưởng app**
1. Vào phần **Nhiệm vụ** trong app
2. Nhận các thưởng có sẵn
3. Mục tiêu: **+35D** từ app

### 💰 **Bước 4: Kiểm tra tổng thưởng**
- **Web (Chrome):** 13D
- **App (BlueStacks):** 35D
- **Tổng:** 48D+ ✅ (đạt yêu cầu 52D)

## ⚠️ **XỬ LÝ LỖI**

### ❌ **Lỗi: Không tìm thấy BlueStacks**
```
❌ Không tìm thấy BlueStacks tại: C:\Program Files\BlueStacks_nxt\HD-Player.exe
```

**Giải pháp:**
1. Kiểm tra BlueStacks đã cài đặt chưa
2. Kiểm tra đường dẫn cài đặt
3. Cài đặt BlueStacks nếu chưa có

### ❌ **Lỗi: Không mở được BlueStacks**
```
❌ Lỗi mở BlueStacks: [chi tiết lỗi]
```

**Giải pháp:**
1. Chạy tool với quyền Administrator
2. Đóng BlueStacks đang chạy và thử lại
3. Khởi động lại máy tính

### ❌ **Lỗi: Instance Pie64 không tồn tại**

**Giải pháp:**
1. Mở BlueStacks Multi-Instance Manager
2. Tạo instance mới tên "Pie64"
3. Hoặc sử dụng instance có sẵn

## 🔧 **TÙY CHỈNH**

### 📝 **Thay đổi đường dẫn BlueStacks:**
Sửa trong file `easy_tool.py`:
```python
bluestacks_path = r"C:\Program Files\BlueStacks_nxt\HD-Player.exe"
```

### 📝 **Thay đổi instance:**
Sửa trong file `easy_tool.py`:
```python
command = [bluestacks_path, "--instance", "Pie64"]
```

## 💡 **TIPS & TRICKS**

### ✅ **Tối ưu hiệu suất:**
1. **Đóng BlueStacks cũ** trước khi chạy tool
2. **Sử dụng instance riêng** cho mỗi tài khoản
3. **Kiểm tra RAM** đủ để chạy nhiều instance

### ✅ **Quản lý nhiều tài khoản:**
1. **Mỗi tài khoản = 1 BlueStacks instance**
2. **Đăng nhập từng tài khoản riêng biệt**
3. **Nhận thưởng từng tài khoản một**

### ✅ **Kiểm tra thành công:**
1. **Chrome:** Kiểm tra "Lịch Sử Nhận" ≥ 13D
2. **BlueStacks:** Kiểm tra số dư trong app ≥ 35D
3. **Tổng:** 13D + 35D = 48D+ ✅

## 🎉 **KẾT QUẢ MONG ĐỢI**

### 📊 **Sau khi hoàn thành:**
- ✅ **5 tài khoản đăng ký thành công**
- ✅ **5 Chrome mở ở trang nhiệm vụ**
- ✅ **5 BlueStacks chạy instance Pie64**
- ✅ **Sẵn sàng nhận thưởng web + app**
- ✅ **Mỗi tài khoản có thể đạt 48D+**

### 🎯 **Mục tiêu cuối:**
**Mỗi tài khoản ≥ 52D = Web (13D) + App (35D) + Bonus**
