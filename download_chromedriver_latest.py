"""
Tải ChromeDriver phù hợp với Chrome version hiện tại
"""

import os
import requests
import zipfile
import subprocess
import json
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def get_chrome_version():
    """L<PERSON>y phiên bản Chrome đã cài đặt"""
    try:
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ]
        
        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                result = subprocess.run([chrome_path, "--version"], 
                                      capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    version = result.stdout.strip().split()[-1]
                    print(f"✅ Chrome version: {version}")
                    return version
        
        print("⚠️  Không tìm thấy Chrome")
        return None
        
    except Exception as e:
        print(f"⚠️  Lỗi khi lấy Chrome version: {e}")
        return None

def get_chromedriver_version(chrome_version):
    """Lấy ChromeDriver version tương thích"""
    try:
        # Lấy major version của Chrome
        major_version = chrome_version.split('.')[0]
        print(f"🔍 Chrome major version: {major_version}")
        
        # URL API để lấy ChromeDriver version tương thích
        api_url = f"https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
        
        print("📡 Đang tìm ChromeDriver tương thích...")
        response = requests.get(api_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            
            # Tìm version ChromeDriver tương thích
            for version_info in reversed(data['versions']):  # Lấy version mới nhất
                if version_info['version'].startswith(major_version + '.'):
                    chromedriver_version = version_info['version']
                    print(f"✅ ChromeDriver version tương thích: {chromedriver_version}")
                    return chromedriver_version
        
        # Fallback: sử dụng version mapping cố định
        version_mapping = {
            "136": "136.0.7103.113",
            "135": "135.0.6790.170",
            "134": "134.0.6767.85",
            "133": "133.0.6835.106",
            "132": "132.0.6834.83"
        }
        
        if major_version in version_mapping:
            chromedriver_version = version_mapping[major_version]
            print(f"✅ Sử dụng version mapping: {chromedriver_version}")
            return chromedriver_version
        
        print(f"⚠️  Không tìm thấy ChromeDriver cho Chrome {major_version}")
        return None
        
    except Exception as e:
        print(f"❌ Lỗi khi tìm ChromeDriver version: {e}")
        return None

def download_chromedriver(version):
    """Tải ChromeDriver với version cụ thể"""
    try:
        # Tạo thư mục drivers
        drivers_dir = "drivers"
        os.makedirs(drivers_dir, exist_ok=True)
        
        # URL tải ChromeDriver
        url = f"https://storage.googleapis.com/chrome-for-testing-public/{version}/win64/chromedriver-win64.zip"
        
        print(f"📥 Đang tải ChromeDriver {version}...")
        print(f"🔗 URL: {url}")
        
        # Tải file
        response = requests.get(url, stream=True, timeout=30)
        if response.status_code == 200:
            zip_path = os.path.join(drivers_dir, "chromedriver.zip")
            
            print("⬇️  Đang tải...")
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print("✅ Đã tải xong")
            
            # Xóa ChromeDriver cũ nếu có
            old_chromedriver = os.path.join(drivers_dir, "chromedriver.exe")
            if os.path.exists(old_chromedriver):
                os.remove(old_chromedriver)
                print("🗑️  Đã xóa ChromeDriver cũ")
            
            # Giải nén
            print("📦 Đang giải nén...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(drivers_dir)
            
            # Di chuyển file từ thư mục con
            extracted_dir = os.path.join(drivers_dir, "chromedriver-win64")
            if os.path.exists(extracted_dir):
                chromedriver_exe = os.path.join(extracted_dir, "chromedriver.exe")
                if os.path.exists(chromedriver_exe):
                    # Di chuyển file ra ngoài
                    import shutil
                    shutil.move(chromedriver_exe, os.path.join(drivers_dir, "chromedriver.exe"))
                    # Xóa thư mục con
                    shutil.rmtree(extracted_dir)
            
            # Xóa file zip
            os.remove(zip_path)
            
            # Kiểm tra file
            chromedriver_path = os.path.join(drivers_dir, "chromedriver.exe")
            if os.path.exists(chromedriver_path):
                print(f"✅ ChromeDriver đã sẵn sàng: {chromedriver_path}")
                
                # Test file
                try:
                    result = subprocess.run([chromedriver_path, "--version"], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        print(f"✅ ChromeDriver version: {result.stdout.strip()}")
                        return True
                    else:
                        print(f"⚠️  ChromeDriver có thể không hoạt động: {result.stderr}")
                        return False
                except Exception as e:
                    print(f"⚠️  Không thể test ChromeDriver: {e}")
                    return False
            else:
                print("❌ Không tìm thấy chromedriver.exe sau khi giải nén")
                return False
        else:
            print(f"❌ Không thể tải ChromeDriver: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi tải ChromeDriver: {e}")
        return False

def main():
    """Main function"""
    print("📥 TẢI CHROMEDRIVER TƯƠNG THÍCH")
    print("="*50)
    
    try:
        # Bước 1: Lấy Chrome version
        chrome_version = get_chrome_version()
        if not chrome_version:
            print(f"{Fore.RED}❌ Không thể lấy Chrome version{Style.RESET_ALL}")
            print("Vui lòng cài đặt Google Chrome")
            return
        
        # Bước 2: Tìm ChromeDriver tương thích
        chromedriver_version = get_chromedriver_version(chrome_version)
        if not chromedriver_version:
            print(f"{Fore.RED}❌ Không tìm thấy ChromeDriver tương thích{Style.RESET_ALL}")
            return
        
        # Bước 3: Tải ChromeDriver
        if download_chromedriver(chromedriver_version):
            print(f"\n{Fore.GREEN}🎉 ĐÃ TẢI CHROMEDRIVER THÀNH CÔNG!{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 ChromeDriver {chromedriver_version} tương thích với Chrome {chrome_version}{Style.RESET_ALL}")
            print(f"{Fore.CYAN}📁 Vị trí: drivers/chromedriver.exe{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.RED}❌ KHÔNG THỂ TẢI CHROMEDRIVER{Style.RESET_ALL}")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
