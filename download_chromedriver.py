"""
Tải ChromeDriver portable
"""

import os
import requests
import zipfile
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def download_chromedriver():
    """Tải ChromeDriver portable"""
    print("📥 TẢI CHROMEDRIVER PORTABLE")
    print("="*50)
    
    # T<PERSON><PERSON> thư mục drivers
    drivers_dir = "drivers"
    os.makedirs(drivers_dir, exist_ok=True)
    
    # URL ChromeDriver ổn định (version cũ hơn, tư<PERSON><PERSON> thích tốt)
    version = "114.0.5735.90"  # Version ổn định
    url = f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_win32.zip"
    
    print(f"📥 Đang tải ChromeDriver version {version}...")
    print(f"🔗 URL: {url}")
    
    try:
        # Tải file
        response = requests.get(url, stream=True)
        if response.status_code == 200:
            zip_path = os.path.join(drivers_dir, "chromedriver.zip")
            
            print("⬇️  Đang tải...")
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print("✅ Đã tải xong")
            
            # Gi<PERSON>i nén
            print("📦 Đang giải nén...")
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(drivers_dir)
            
            # Xóa file zip
            os.remove(zip_path)
            
            # Kiểm tra file
            chromedriver_path = os.path.join(drivers_dir, "chromedriver.exe")
            if os.path.exists(chromedriver_path):
                print(f"✅ ChromeDriver đã sẵn sàng: {chromedriver_path}")
                
                # Test file
                try:
                    import subprocess
                    result = subprocess.run([chromedriver_path, "--version"], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        print(f"✅ ChromeDriver version: {result.stdout.strip()}")
                        return True
                    else:
                        print(f"⚠️  ChromeDriver có thể không hoạt động: {result.stderr}")
                        return False
                except Exception as e:
                    print(f"⚠️  Không thể test ChromeDriver: {e}")
                    return False
            else:
                print("❌ Không tìm thấy chromedriver.exe sau khi giải nén")
                return False
        else:
            print(f"❌ Không thể tải ChromeDriver: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi khi tải ChromeDriver: {e}")
        return False

def main():
    """Main function"""
    try:
        if download_chromedriver():
            print(f"\n{Fore.GREEN}🎉 ĐÃ TẢI CHROMEDRIVER THÀNH CÔNG!{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 Tool sẽ tự động sử dụng ChromeDriver này{Style.RESET_ALL}")
            print(f"{Fore.CYAN}📁 Vị trí: drivers/chromedriver.exe{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.RED}❌ KHÔNG THỂ TẢI CHROMEDRIVER{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 Thử các giải pháp khác:{Style.RESET_ALL}")
            print("1. Kiểm tra kết nối internet")
            print("2. Tắt antivirus/firewall tạm thời")
            print("3. Chạy với quyền Administrator")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
