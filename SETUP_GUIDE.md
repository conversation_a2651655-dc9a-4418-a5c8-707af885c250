# 🚀 **HƯỚNG DẪN SETUP - 13WIN ACCOUNT MANAGER**

## 📋 **YÊU CẦU HỆ THỐNG**

### 💻 **<PERSON><PERSON><PERSON> mềm cần thiết:**
- ✅ **Windows 10/11** (64-bit)
- ✅ **.NET 6.0 Runtime** hoặc cao hơn
- ✅ **Python 3.8+** (cho tool đăng ký)
- ✅ **Chrome Browser** (cho tool đăng ký)
- ✅ **BlueStacks** (tùy chọn, cho app)

### 🔧 **Cài đặt .NET 6.0:**
1. T<PERSON><PERSON> từ: https://dotnet.microsoft.com/download/dotnet/6.0
2. Chọn **".NET Desktop Runtime 6.0.x"** (Windows x64)
3. Cài đặt và khởi động lại máy

### 🐍 **Cài đặt Python:**
1. Tải từ: https://www.python.org/downloads/
2. **Quan trọng:** Tick "Add Python to PATH"
3. Cài đặt packages: `pip install selenium colorama`

## 🛠️ **SETUP PROJECT**

### 📁 **<PERSON><PERSON><PERSON> trúc thư mục:**
```
13win-tools/
├── AccountManager.exe          # GUI Tool (sau khi build)
├── easy_tool.py               # Python registration tool
├── accounts.txt               # Danh sách tài khoản
├── account_status.json        # Trạng thái tài khoản (auto-generated)
├── screenshots/               # Screenshots từ tool Python
├── chromedriver.exe           # Chrome driver
└── README_GUI.md              # Hướng dẫn
```

### 🔨 **Build GUI Tool:**

**Cách 1: Sử dụng build script**
```bash
# Chạy file build.bat
build.bat
```

**Cách 2: Manual build**
```bash
# Restore packages
dotnet restore

# Build project
dotnet build -c Release

# Publish executable
dotnet publish -c Release -r win-x64 --self-contained
```

### 🚀 **Chạy ứng dụng:**

**Cách 1: Sử dụng run script**
```bash
# Chạy file run.bat
run.bat
```

**Cách 2: Manual run**
```bash
# Chạy executable
bin\Release\net6.0-windows\win-x64\publish\AccountManager.exe
```

## 📊 **SETUP DỮ LIỆU**

### 📝 **Tạo file accounts.txt:**
```
taolatrumnohu1|password123|TRAN HOANG AN
taolatrumnohu2|password456|TRAN HOANG AN
taolatrumnohu3|password789|TRAN HOANG AN
```

**Format:** `username|password|realname`

### 🎯 **Import vào GUI:**
1. Mở **AccountManager.exe**
2. **File → Import từ file**
3. Chọn file **accounts.txt**
4. Tài khoản sẽ hiển thị trong danh sách

## 🔧 **CẤU HÌNH TOOL PYTHON**

### 📦 **Cài đặt dependencies:**
```bash
pip install selenium colorama
```

### 🌐 **Download ChromeDriver:**
1. Kiểm tra phiên bản Chrome: `chrome://version/`
2. Tải ChromeDriver: https://chromedriver.chromium.org/
3. Đặt `chromedriver.exe` cùng thư mục với `easy_tool.py`

### 📱 **Cài đặt BlueStacks (tùy chọn):**
1. Tải từ: https://www.bluestacks.com/
2. Cài đặt với đường dẫn mặc định: `C:\Program Files\BlueStacks_nxt\`
3. Tạo instance tên "Pie64" (hoặc sử dụng instance có sẵn)

## 🚀 **CHẠY TOOL**

### 1️⃣ **Khởi động GUI:**
```bash
AccountManager.exe
```

### 2️⃣ **Import tài khoản:**
- **File → Import từ file** → Chọn `accounts.txt`
- Hoặc **thêm tài khoản thủ công**

### 3️⃣ **Chạy tool đăng ký:**
- **Tools → Chạy tool đăng ký**
- Tool Python sẽ được khởi chạy
- GUI sẽ theo dõi và cập nhật trạng thái

### 4️⃣ **Theo dõi tiến trình:**
- Trạng thái được cập nhật real-time
- Màu sắc thể hiện trạng thái hiện tại
- Screenshots được lưu trong thư mục `screenshots/`

## 🎨 **GIAO DIỆN GUI**

### 🖥️ **Main Window:**
- **Danh sách tài khoản** với trạng thái màu sắc
- **Toolbar** với các nút chức năng
- **Status bar** hiển thị thống kê
- **Menu** với các tùy chọn nâng cao

### 🎯 **Trạng thái tài khoản:**
- 🔘 **Chưa bắt đầu** (Gray)
- 🟠 **Đang đăng ký** (Orange)  
- 🔵 **Đăng ký thành công** (Blue)
- 🔴 **Đăng ký thất bại** (Red)
- 🟢 **Đã nhận thưởng web** (Green)
- 🟣 **Đã nhận thưởng app** (Purple)
- 🟢 **Hoàn thành** (Dark Green)

### ✏️ **Chỉnh sửa tài khoản:**
- **Double-click** để mở dialog chỉnh sửa
- Cập nhật trạng thái, thưởng, ghi chú
- Tự động lưu vào `account_status.json`

## 🔄 **TÍCH HỢP PYTHON TOOL**

### 📂 **Files được chia sẻ:**
- **`accounts.txt`** - Input cho tool Python
- **`account_status.json`** - Output từ tool Python
- **`screenshots/`** - Screenshots từ quá trình đăng ký

### 🔄 **Quy trình:**
1. **GUI** đọc `accounts.txt` và hiển thị danh sách
2. **User** click "Chạy tool" → GUI khởi chạy Python tool
3. **Python tool** đăng ký và cập nhật `account_status.json`
4. **GUI** monitor file changes và auto-refresh
5. **Kết quả** hiển thị real-time trong GUI

## 🐛 **XỬ LÝ LỖI**

### ❌ **Lỗi thường gặp:**

**1. Không tìm thấy .NET:**
```
This application requires .NET Desktop Runtime 6.0
```
**Giải pháp:** Cài đặt .NET 6.0 Desktop Runtime

**2. Không chạy được Python tool:**
```
Lỗi chạy tool: The system cannot find the file specified
```
**Giải pháp:** 
- Cài đặt Python và thêm vào PATH
- Đảm bảo `easy_tool.py` cùng thư mục với GUI

**3. Không load được accounts.txt:**
```
Lỗi load tài khoản: File not found
```
**Giải pháp:** Tạo file `accounts.txt` với format đúng

**4. ChromeDriver lỗi:**
```
SessionNotCreatedException: This version of ChromeDriver only supports Chrome version X
```
**Giải pháp:** Tải ChromeDriver phù hợp với phiên bản Chrome

## 🎉 **HOÀN TẤT SETUP**

### ✅ **Kiểm tra setup thành công:**
1. **GUI khởi động** không lỗi
2. **Import accounts.txt** thành công
3. **Chạy tool Python** từ GUI hoạt động
4. **Trạng thái cập nhật** real-time
5. **Screenshots** được tạo trong thư mục

### 🚀 **Sẵn sàng sử dụng:**
- ✅ **GUI quản lý tài khoản** hiệu quả
- ✅ **Tool Python đăng ký** tự động
- ✅ **BlueStacks** sẵn sàng cho app
- ✅ **Theo dõi tiến trình** real-time
- ✅ **Đạt mục tiêu 52D** dễ dàng

**🎯 Tool đã sẵn sàng để quản lý và đăng ký tài khoản 13win!**
