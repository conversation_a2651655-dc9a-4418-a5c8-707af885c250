# 🎯 TOOL 13WIN - ĐĂNG KÝ + NHẬN THƯỞNG

## 📁 Tool duy nhất

**`easy_tool.py`** - Tool đăng ký dễ dàng, không lỗi ⭐ **KHUYẾN NGHỊ**

## 🚀 Cách sử dụng

### Chạy tool:
```bash
python easy_tool.py
```

## 🌟 TÍNH NĂNG TOOL

### Ưu điểm:
- ✅ **Mỗi tài khoản = 1 Chrome riêng** → Không conflict session
- ✅ **Không lỗi tạo thư mục** → Không dùng profile phức tạp
- ✅ **Tự động điều hướng task** → Đến trang có hồng bao
- ✅ **Tự động mở hồng bao** → Click nút MỞ ở trang task
- ✅ **Tự động nhận nhiệm vụ** → Click tất cả nút Nhận
- ✅ **Kiểm tra Lịch Sử Nhận** → Đ<PERSON><PERSON> bảo tối thiểu 13D
- ✅ **Đơ<PERSON> giả<PERSON>, <PERSON><PERSON> đị<PERSON>** → <PERSON>h<PERSON><PERSON> phức tạp, ch<PERSON><PERSON> mượt

### Quy trình:
```
🎯 TOOL ĐĂNG KÝ DỄ DÀNG
========================================
👤 Prefix username: taolatrumnohu
📝 Số lượng tài khoản: 5
📝 Bắt đầu từ số: 1

📋 Sẽ đăng ký 5 tài khoản:
👤 taolatrumnohu1 → taolatrumnohu5
👨 Họ tên: TRAN HOANG AN
🌐 Mỗi tài khoản = 1 Chrome riêng
```

### Kết quả:
```
📊 1/5: taolatrumnohu1
✅ Chrome cho taolatrumnohu1
📝 Tìm thấy 4 input
✅ Username: taolatrumnohu1
✅ Password
✅ Confirm Password
✅ Fullname: TRAN HOANG AN
🚀 Submit
🎉 ĐĂNG KÝ THÀNH CÔNG!
🧧 Xử lý hồng bao và nhiệm vụ...
📋 Điều hướng đến trang nhiệm vụ...
✅ Đã điều hướng đến: https://www.13win16.com/task
🧧 Mở hồng bao...
✅ Đã mở hồng bao
🎁 Tìm và click nút Nhận...
✅ Đã nhận thưởng 1
✅ Đã nhận thưởng 2
📊 Kiểm tra Lịch Sử Nhận...
✅ Đã click Lịch Sử Nhận
💰 Tổng thưởng đã nhận: 14.16D
🎁 Đã nhận 2 phần thưởng
💰 Tổng thưởng: 14.16D
💰 Tài khoản đã đạt tối thiểu 13D ✅

📊 KẾT QUẢ CUỐI:
✅ Đăng ký thành công: 5
❌ Đăng ký thất bại: 0
🌐 Chrome đang chạy: 5
📁 Tài khoản lưu trong: accounts.txt
📸 Screenshot trong: screenshots/

💰 TÌNH TRẠNG THƯỞNG:
📊 Đã tự động xử lý hồng bao và nhiệm vụ
🎯 Mỗi tài khoản đã kiểm tra tối thiểu 13D
📱 Để đạt 52D cần thêm thưởng từ app (+35D)

📱 HƯỚNG DẪN NHẬN THÊM THƯỞNG:
1. Tải app 13win trên điện thoại/BlueStacks
2. Đăng nhập từng tài khoản trong app
3. Nhận thưởng app (+35D)
4. Tổng: 13D + 35D = 48D+ ✅

🎉 QUY TRÌNH HOÀN CHỈNH:
→ Đã xử lý hồng bao tự động
→ Đã nhận thưởng nhiệm vụ web
→ Kiểm tra Lịch Sử Nhận để xác nhận
→ Cần app để đạt đủ 52D

🌐 Hiện có 5 Chrome đang chạy
Đóng tất cả Chrome? (y/n): n
🌐 Chrome vẫn mở để bạn kiểm tra thủ công
💡 Mỗi Chrome đang ở trang thưởng, sẵn sàng nhận thưởng
```

## 🎯 Chức năng 1: Đăng ký 1 tài khoản

### Quy trình:
1. **Nhập thông tin:**
   - Username: Tự nhập
   - Password: Để trống = tự động tạo
   - Họ tên: Mặc định "TRAN HOANG AN"

2. **Tự động đăng ký**

3. **Hỏi có muốn nhận thưởng không**
   - Chọn "y" = tự động đăng nhập và nhận thưởng
   - Chọn "n" = chỉ đăng ký

### Ví dụ:
```
👤 Username: taolatrumnohu1
🔒 Password (để trống = tự động):
👨 Họ tên (mặc định: TRAN HOANG AN):

🔒 Password tự động: abc12345123
```

## 🚀 Chức năng 2: Đăng ký hàng loạt (Nhiều tab)

### Quy trình:
1. **Cấu hình:**
   - Prefix: Mặc định "taolatrumnohu"
   - Số lượng: Nhập số tài khoản cần tạo
   - Bắt đầu từ số: Mặc định 1

2. **GIAI ĐOẠN 1: Đăng ký**
   - Mỗi tài khoản đăng ký trong tab riêng
   - Tránh session bị ghi đè
   - Username: taolatrumnohu1, taolatrumnohu2, ...
   - Password: Tự động tạo khác nhau
   - Họ tên: "TRAN HOANG AN" (cố định)

3. **GIAI ĐOẠN 2: Nhận thưởng**
   - Chỉ nhận thưởng cho tài khoản đăng ký thành công
   - Chuyển đến từng tab để nhận thưởng
   - Giữ nguyên session của mỗi tài khoản

### Ví dụ:
```
👤 Prefix username (mặc định: taolatrumnohu):
📝 Số lượng: 5
📝 Bắt đầu từ số (mặc định: 1): 1

📋 Sẽ đăng ký 5 tài khoản:
👤 taolatrumnohu1 → taolatrumnohu5
👨 Họ tên: TRAN HOANG AN
📑 Mỗi tài khoản sẽ đăng ký trong tab riêng
```

### Kết quả:
```
🔥 GIAI ĐOẠN 1: ĐĂNG KÝ 5 TÀI KHOẢN
✅ Thành công tab 1: taolatrumnohu1
💰 taolatrumnohu1: 22/52 D
✅ Thành công tab 2: taolatrumnohu2
💰 taolatrumnohu2: 22/52 D

🎁 GIAI ĐOẠN 2: NHẬN THƯỞNG CHO 2 TÀI KHOẢN
✅ Đã nhận thưởng: taolatrumnohu1
💰 taolatrumnohu1: 57/52 D

💰 TỔNG KẾT THƯỞNG:
✅ taolatrumnohu1: 57/52 D
⏳ taolatrumnohu2: 22/52 D
   📝 Còn thiếu: 30 D
   📱 Cần đăng nhập app: +35 D

📊 Hoàn thành: 1/2 tài khoản

⚠️ CÒN 1 TÀI KHOẢN CHƯA ĐẠT MỤC TIÊU 52D
📱 Cần đăng nhập app để nhận thêm 35D/tài khoản
```

## 📊 Kết quả

### File lưu tài khoản:
**`accounts.txt`** - Format: `username|password|fullname`

Ví dụ:
```
taolatrumnohu1|abc12345123|TRAN HOANG AN
taolatrumnohu2|def67890456|TRAN HOANG AN
```

### Folder screenshot:
**`screenshots/`** - Chứa tất cả ảnh chụp màn hình

Các ảnh được tạo:
- `01_[username]_register.png` - Trang đăng ký
- `02_[username]_filled.png` - Form đã điền
- `03_[username]_result.png` - Kết quả đăng ký
- `04_[username]_task_page.png` - Trang nhiệm vụ (có hồng bao)
- `05_[username]_after_open_envelope.png` - Sau khi mở hồng bao
- `06_[username]_after_claim.png` - Sau khi nhận thưởng
- `07_[username]_reward_history.png` - Lịch sử nhận thưởng

### Thống kê:
```
📊 KẾT QUẢ:
✅ Thành công: 4
❌ Thất bại: 1
📁 Tài khoản lưu trong: accounts.txt
```

## 🎁 Nhận thưởng - MỤC TIÊU 52 D/TÀI KHOẢN

### Thưởng tự động:
- **Đăng ký tài khoản**: 13.00 D
- **Hồng bao đăng ký**: ~5.00 D (tự động click)
- **Cài đặt phương thức liên lạc**: 4.00 D
- **Tổng web**: ~22 D/tài khoản

### Thưởng app (cần thủ công):
- **Tải xuống, đăng nhập**: 35.00 D
- **TỔNG MỤC TIÊU**: 52+ D/tài khoản

### Tính năng theo dõi thưởng 💰:
- **Theo dõi từng tài khoản** đã nhận bao nhiêu D
- **Hiển thị tiến trình** đạt mục tiêu 52D
- **Chỉ đóng browser** khi TẤT CẢ tài khoản đạt 52D
- **Hướng dẫn** nhận thưởng app cho tài khoản chưa đủ

### Tính năng hồng bao 🧧:
- **Tự động phát hiện** hồng bao sau khi đăng ký thành công
- **Tự động click** để nhận thưởng trong hồng bao
- **Chụp screenshot** trước và sau khi click hồng bao
- **Đóng popup** tự động sau khi nhận

### Quy trình hoàn chỉnh:
1. **Đăng ký** → Nhận 13D + hồng bao (~5D) + liên lạc (4D) = ~22D
2. **Đăng nhập app** → Nhận thêm 35D
3. **Tổng**: 52+ D/tài khoản ✅

## 🔧 Yêu cầu

- ✅ Python 3.7+
- ✅ Chrome browser
- ✅ ChromeDriver trong thư mục `drivers/`

### Cài đặt:
```bash
pip install selenium colorama
```

## 💡 Tips

1. **Test với 1 tài khoản trước** khi chạy hàng loạt
2. **Backup file accounts.txt** thường xuyên
3. **Chạy vào giờ ít người** để tránh quá tải
4. **Không chạy quá nhanh** (tool có delay tự động)
5. **Đăng ký hàng loạt sử dụng nhiều tab** để tránh session bị ghi đè
6. **Chỉ nhận thưởng cho tài khoản đăng ký thành công**
7. **Có thể để browser mở** để kiểm tra thủ công sau khi tool chạy xong

## 🆘 Troubleshooting

### Lỗi ChromeDriver:
```
❌ Lỗi khởi tạo: 'chromedriver' executable needs to be in PATH
```
**Giải pháp**: Tải ChromeDriver và đặt trong `drivers/chromedriver.exe`

### Lỗi không tìm thấy element:
```
❌ Lỗi điền form: Message: no such element
```
**Giải pháp**: Website có thể đã thay đổi, thử lại sau

### Tài khoản đã tồn tại:
```
⚠️ Tài khoản đã tồn tại!
```
**Giải pháp**: Thay đổi prefix hoặc số bắt đầu

## 🎉 Kết luận

Tool này đơn giản, hiệu quả và ổn định:
- ✅ **1 file duy nhất** - Không rối rắm
- ✅ **Không lỗi tạo thư mục** - Chạy mượt trên Windows
- ✅ **Mỗi tài khoản = 1 Chrome** - Không conflict
- ✅ **Tự động điều hướng task** - Đến trang có hồng bao
- ✅ **Tự động mở hồng bao** - Click nút MỞ ở trang task
- ✅ **Tự động nhận nhiệm vụ** - Click tất cả nút Nhận
- ✅ **Kiểm tra Lịch Sử Nhận** - Đảm bảo tối thiểu 13D
- ✅ **Hướng dẫn rõ ràng** - Biết cần làm gì

**Chúc bạn sử dụng hiệu quả! 🎁**
