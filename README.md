# 🎯 TOOL 13WIN - ĐĂNG KÝ + NHẬN THƯỞNG

## 📁 File duy nhất cần dùng

**`final_tool.py`** - <PERSON><PERSON> chỉnh, đ<PERSON><PERSON> g<PERSON>, d<PERSON> sử dụng

## 🚀 Cách sử dụng

### Chạy tool:
```bash
python final_tool.py
```

### Chọn chức năng:
```
🎯 TOOL 13WIN - CHỌN CHỨC NĂNG
========================================
1. Đăng ký 1 tài khoản + nhận thưởng
2. Đăng ký hàng loạt

Chọn (1/2):
```

## 🎯 Chức năng 1: Đăng ký 1 tài khoản

### Quy trình:
1. **Nhập thông tin:**
   - Username: Tự nhập
   - Password: Để trống = tự động tạo
   - Họ tên: Mặc định "TRAN HOANG AN"

2. **Tự động đăng ký**

3. **Hỏi có muốn nhận thưởng không**
   - <PERSON>ọ<PERSON> "y" = tự động đăng nhập và nhận thưởng
   - Chọn "n" = chỉ đăng ký

### Ví dụ:
```
👤 Username: taolatrumnohu1
🔒 Password (để trống = tự động):
👨 Họ tên (mặc định: TRAN HOANG AN):

🔒 Password tự động: abc12345123
```

## 🚀 Chức năng 2: Đăng ký hàng loạt (Nhiều tab)

### Quy trình:
1. **Cấu hình:**
   - Prefix: Mặc định "taolatrumnohu"
   - Số lượng: Nhập số tài khoản cần tạo
   - Bắt đầu từ số: Mặc định 1

2. **GIAI ĐOẠN 1: Đăng ký**
   - Mỗi tài khoản đăng ký trong tab riêng
   - Tránh session bị ghi đè
   - Username: taolatrumnohu1, taolatrumnohu2, ...
   - Password: Tự động tạo khác nhau
   - Họ tên: "TRAN HOANG AN" (cố định)

3. **GIAI ĐOẠN 2: Nhận thưởng**
   - Chỉ nhận thưởng cho tài khoản đăng ký thành công
   - Chuyển đến từng tab để nhận thưởng
   - Giữ nguyên session của mỗi tài khoản

### Ví dụ:
```
👤 Prefix username (mặc định: taolatrumnohu):
📝 Số lượng: 5
📝 Bắt đầu từ số (mặc định: 1): 1

📋 Sẽ đăng ký 5 tài khoản:
👤 taolatrumnohu1 → taolatrumnohu5
👨 Họ tên: TRAN HOANG AN
📑 Mỗi tài khoản sẽ đăng ký trong tab riêng
```

### Kết quả:
```
🔥 GIAI ĐOẠN 1: ĐĂNG KÝ 5 TÀI KHOẢN
✅ Thành công tab 1: taolatrumnohu1
✅ Thành công tab 2: taolatrumnohu2
✅ Thành công tab 3: taolatrumnohu3

🎁 GIAI ĐOẠN 2: NHẬN THƯỞNG CHO 3 TÀI KHOẢN
✅ Đã nhận thưởng: taolatrumnohu1
✅ Đã nhận thưởng: taolatrumnohu2
✅ Đã nhận thưởng: taolatrumnohu3
```

## 📊 Kết quả

### File lưu tài khoản:
**`accounts.txt`** - Format: `username|password|fullname`

Ví dụ:
```
taolatrumnohu1|abc12345123|TRAN HOANG AN
taolatrumnohu2|def67890456|TRAN HOANG AN
```

### Folder screenshot:
**`chụp màn hình/`** - Chứa tất cả ảnh chụp màn hình

Các ảnh được tạo:
- `01_register_page_[username].png` - Trang đăng ký
- `02_filled_form_[username].png` - Form đã điền
- `03_register_result_[username].png` - Kết quả đăng ký
- `04_red_envelope_found_[username].png` - Tìm thấy hồng bao
- `05_red_envelope_clicked_[username].png` - Sau khi click hồng bao
- `06_task_page_[username].png` - Trang nhiệm vụ
- `07_after_claim_[username].png` - Sau khi nhận thưởng

### Thống kê:
```
📊 KẾT QUẢ:
✅ Thành công: 4
❌ Thất bại: 1
📁 Tài khoản lưu trong: accounts.txt
```

## 🎁 Nhận thưởng

### Thưởng tự động:
- **Đăng ký tài khoản**: 13.00 D
- **Hồng bao đăng ký**: Tự động click sau khi đăng ký thành công
- **Cài đặt phương thức liên lạc**: 4.00 D
- **Tổng**: 17+ D/tài khoản

### Tính năng hồng bao 🧧:
- **Tự động phát hiện** hồng bao sau khi đăng ký thành công
- **Tự động click** để nhận thưởng trong hồng bao
- **Chụp screenshot** trước và sau khi click hồng bao
- **Đóng popup** tự động sau khi nhận

### Thưởng app (thủ công):
- **Tải xuống, đăng nhập**: 35.00 D
- Cần đăng nhập app trên điện thoại/BlueStacks

## 🔧 Yêu cầu

- ✅ Python 3.7+
- ✅ Chrome browser
- ✅ ChromeDriver trong thư mục `drivers/`

### Cài đặt:
```bash
pip install selenium colorama
```

## 💡 Tips

1. **Test với 1 tài khoản trước** khi chạy hàng loạt
2. **Backup file accounts.txt** thường xuyên
3. **Chạy vào giờ ít người** để tránh quá tải
4. **Không chạy quá nhanh** (tool có delay tự động)
5. **Đăng ký hàng loạt sử dụng nhiều tab** để tránh session bị ghi đè
6. **Chỉ nhận thưởng cho tài khoản đăng ký thành công**
7. **Có thể để browser mở** để kiểm tra thủ công sau khi tool chạy xong

## 🆘 Troubleshooting

### Lỗi ChromeDriver:
```
❌ Lỗi khởi tạo: 'chromedriver' executable needs to be in PATH
```
**Giải pháp**: Tải ChromeDriver và đặt trong `drivers/chromedriver.exe`

### Lỗi không tìm thấy element:
```
❌ Lỗi điền form: Message: no such element
```
**Giải pháp**: Website có thể đã thay đổi, thử lại sau

### Tài khoản đã tồn tại:
```
⚠️ Tài khoản đã tồn tại!
```
**Giải pháp**: Thay đổi prefix hoặc số bắt đầu

## 🎉 Kết luận

Tool này đơn giản, hiệu quả và dễ sử dụng:
- ✅ **1 file duy nhất** - Không rối rắm
- ✅ **2 chức năng chính** - Đủ dùng
- ✅ **Tự động hóa** - Tiết kiệm thời gian
- ✅ **Lưu kết quả** - Dễ quản lý

**Chúc bạn sử dụng hiệu quả! 🎁**
