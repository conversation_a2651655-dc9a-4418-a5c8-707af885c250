# Auto Registration Tool - 13win16.com

Tool tự động đăng ký tài khoản cho website 13win16.com với proxy khác nhau cho mỗi trình duyệt.

## 🚀 Tính năng

- ✅ **Tạo username tuần tự** (taolatrumnohu1, taolatrumnohu2, ...)
- 🔄 **Xử lý tài khoản đã tồn tại** - Tự động tạo username ngẫu nhiên
- 👤 **Họ tên cố định** - Sử dụng số tài khoản ngân hàng
- 📝 **Nhập thông tin thủ công** (tê<PERSON> đ<PERSON><PERSON> nh<PERSON>p, mật khẩu, họ tên thật)
- 🌐 Sử dụng proxy miễn phí khác nhau cho mỗi trình duyệt
- 🔄 Chạy đa luồng (nhiều trình duyệt đồng thời)
- 🖥️ **Kết nối với trình duyệt hiện có** của máy để auto điền
- 📊 <PERSON> dõi kết quả real-time
- 💾 Lưu thông tin tài khoản thành công với timestamp
- 🎯 Tùy chỉnh số lượng tài khoản
- 🔍 Tự động phân tích form đăng ký
- 📸 Chụp screenshot để debug

## 📋 Yêu cầu

- Python 3.7+
- Chrome Browser
- Internet connection

## 🛠️ Cài đặt

1. **Clone hoặc tải project:**
```bash
git clone <repository-url>
cd chrome-auto
```

2. **Cài đặt dependencies:**
```bash
pip install -r requirements.txt
```

3. **Chạy tool:**
```bash
python start_tool.py
```
hoặc
```bash
python main.py
```

## 📖 Hướng dẫn sử dụng

### 🎯 Cách 1: Sử dụng trình duyệt hiện có (Khuyến nghị)

1. **Khởi động Chrome debug mode:**
```bash
python start_chrome_debug.py
```
hoặc
```bash
start_chrome_debug.bat
```

2. **Test kết nối (tùy chọn):**
```bash
python test_chrome_connection.py
```

3. **Chạy tool:**
```bash
python main.py
```

4. **Chọn cấu hình:**
   - Chế độ: 1 (Tự động) hoặc 2 (Thủ công)
   - Nếu chọn thủ công: nhập tên đăng nhập, mật khẩu, họ tên
   - Sử dụng trình duyệt hiện có: y
   - Sử dụng proxy: y/n

### 🎯 Cách 2: Tạo trình duyệt mới

1. **Chạy tool:**
```bash
python main.py
```

2. **Chọn cấu hình:**
   - Chế độ: 1 (Tự động) hoặc 2 (Thủ công)
   - Số lượng tài khoản muốn tạo
   - Số trình duyệt chạy đồng thời (1-5)
   - Sử dụng trình duyệt hiện có: n
   - Sử dụng proxy: y/n

3. **Tool sẽ tự động:**
   - Tìm và test proxy miễn phí
   - Phân tích form đăng ký
   - Tạo thông tin tài khoản ngẫu nhiên hoặc sử dụng thông tin thủ công
   - Đăng ký tài khoản với proxy khác nhau

## 📁 Cấu trúc file

```
chrome-auto/
├── main.py                    # File chính
├── start_tool.py              # Script khởi động với encoding
├── start_chrome_debug.py      # Khởi động Chrome debug mode
├── start_chrome_debug.bat     # Batch file khởi động Chrome
├── test_chrome_connection.py  # Test kết nối Chrome debug
├── browser_manager.py         # Quản lý trình duyệt
├── proxy_manager.py           # Quản lý proxy
├── registration_bot.py        # Bot đăng ký
├── account_generator.py       # Tạo thông tin tài khoản
├── config.py                  # Cấu hình
├── test_tool.py              # Test các chức năng
├── setup.py                  # Script cài đặt
├── run_tool.bat              # Menu batch file
├── requirements.txt          # Dependencies
├── README.md                 # Hướng dẫn
└── output/                   # Thư mục kết quả
    ├── successful_accounts.txt
    ├── failed_accounts.txt
    ├── working_proxies.txt
    └── registration.log
```

## ⚙️ Cấu hình

### 📝 Cấu hình Username và Họ Tên

Chỉnh sửa file `config.py`:

```python
ACCOUNT_CONFIG = {
    "username_prefix": "taolatrumnohu",  # Prefix cho username
    "fixed_real_name": "Nguyễn Văn A",  # Họ tên cố định (số tài khoản ngân hàng)
    # ... các cấu hình khác
}
```

### 🔧 Các cấu hình khác:

- **BROWSER_CONFIG**: Cấu hình trình duyệt
- **PROXY_CONFIG**: Cấu hình proxy
- **REGISTRATION_CONFIG**: Cấu hình đăng ký
- **ACCOUNT_CONFIG**: Cấu hình thông tin tài khoản

### 📊 Logic tạo username:

1. **Lần đầu:** `taolatrumnohu1`, `taolatrumnohu2`, `taolatrumnohu3`, ...
2. **Nếu đã tồn tại:** `taolatrumnohu12345`, `taolatrumnohu67890`, ... (ngẫu nhiên)
3. **Họ tên:** Luôn sử dụng giá trị cố định từ config

## 📊 Kết quả

Tool sẽ tạo các file kết quả:

- `successful_accounts.txt`: Danh sách tài khoản đăng ký thành công
- `failed_accounts.txt`: Danh sách tài khoản thất bại
- `working_proxies.txt`: Danh sách proxy hoạt động
- `registration.log`: Log chi tiết
- `page_source.html`: Source code trang đăng ký
- `registration_form.png`: Screenshot form đăng ký

## 🔧 Troubleshooting

### Lỗi thường gặp:

1. **Lỗi khởi động Chrome debug:**
   ```
   ModuleNotFoundError: No module named 'psutil'
   ```
   - **Giải pháp:** Đã được sửa, không cần psutil nữa
   - Chạy: `python start_chrome_debug.py`

2. **Không kết nối được Chrome debug:**
   ```
   Không thể kết nối với trình duyệt hiện có
   ```
   - **Giải pháp:**
     - Chạy: `python start_chrome_debug.py`
     - Kiểm tra Chrome đã mở với port 9222
     - Test: `python test_chrome_connection.py`

3. **ChromeDriver không tìm thấy:**
   - Tool sẽ tự động tải ChromeDriver
   - Đảm bảo Chrome Browser đã cài đặt

4. **Không tìm thấy proxy:**
   - Chọn "n" khi hỏi sử dụng proxy
   - Hoặc chờ tool tìm proxy mới

5. **Form đăng ký thay đổi:**
   - Tool sẽ tự động phân tích form
   - Kiểm tra file `page_source.html` và `registration_form.png`

6. **Captcha:**
   - Tool sẽ dừng để user nhập captcha thủ công
   - Nhấn Enter sau khi nhập xong

7. **Tool dừng sau khi hiển thị "Proxy hoạt động":**
   ```
   2025-05-27 12:05:15,695 - INFO - Proxy hoạt động: 154.65.39.8:80
   2025-05-27 12:05:26,639 - INFO - Proxy hoạt động: 47.237.92.86:9080
   ```
   - **Nguyên nhân:** Tool đang test proxy (mất 1-2 phút), chưa bị treo
   - **Giải pháp:** Chờ tool hoàn thành hoặc sử dụng:
     ```bash
     python run_with_progress.py  # Hiển thị tiến trình
     python show_log.py          # Xem log real-time
     ```

8. **Lỗi đọc file log:**
   ```
   'charmap' codec can't decode byte 0x90 in position 7
   ```
   - **Nguyên nhân:** File log cũ có encoding không tương thích
   - **Giải pháp nhanh:**
     ```bash
     python clean_start.py    # Khởi động sạch (Khuyến nghị)
     delete_logs.bat          # Hoặc xóa file log thủ công
     ```

9. **Lỗi encoding tiếng Việt:**
   ```
   UnicodeEncodeError: 'charmap' codec can't encode character
   ```
   - **Giải pháp:** Đã được sửa tự động
   - Sử dụng: `python start_tool.py`

## ⚠️ Lưu ý

- Tool chỉ dành cho mục đích học tập và test
- Tuân thủ Terms of Service của website
- Sử dụng có trách nhiệm
- Không spam hoặc lạm dụng

## 🤝 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra file log: `registration.log`
2. Xem screenshot: `registration_form.png`
3. Kiểm tra source code: `page_source.html`

## 📝 Changelog

### v1.0.0
- Tính năng cơ bản đăng ký tài khoản
- Hỗ trợ proxy miễn phí
- Đa luồng
- Tự động phân tích form
