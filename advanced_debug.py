"""
Tool debug nâng cao để phân tích chi tiết form đăng ký
Tìm hiểu tại sao không tìm thấy ô password
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def advanced_form_analysis():
    """Phân tích chi tiết form đăng ký"""
    print("🔍 PHÂN TÍCH NÂNG CAO FORM ĐĂNG KÝ")
    print("="*60)
    
    driver = None
    try:
        # Cấu hình Chrome
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1366,768')
        
        chromedriver_path = "drivers/chromedriver.exe"
        service = Service(chromedriver_path)
        
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(30)
        driver.implicitly_wait(2)
        
        # Điều hướng đến trang đăng ký
        register_url = "https://www.13win16.com/home/<USER>"
        print(f"🌐 Đi đến: {register_url}")
        driver.get(register_url)
        
        # Chờ trang load hoàn toàn
        print("⏳ Chờ trang load...")
        time.sleep(10)
        
        # Chụp screenshot đầy đủ
        driver.save_screenshot("full_page_analysis.png")
        print("📸 Đã chụp screenshot: full_page_analysis.png")
        
        print(f"\n📋 PHÂN TÍCH CHI TIẾT:")
        print("="*50)
        
        # 1. Phân tích tất cả elements có thể là input
        print("\n1️⃣ TẤT CẢ ELEMENTS CÓ THỂ LÀ INPUT:")
        
        all_inputs = driver.find_elements(By.CSS_SELECTOR, '*[type], input, textarea')
        input_data = []
        
        for i, element in enumerate(all_inputs):
            try:
                tag_name = element.tag_name
                element_type = element.get_attribute('type') or 'không có'
                placeholder = element.get_attribute('placeholder') or 'không có'
                name = element.get_attribute('name') or 'không có'
                id_attr = element.get_attribute('id') or 'không có'
                class_attr = element.get_attribute('class') or 'không có'
                value = element.get_attribute('value') or 'không có'
                is_displayed = element.is_displayed()
                is_enabled = element.is_enabled()
                
                element_info = {
                    'index': i+1,
                    'tag': tag_name,
                    'type': element_type,
                    'placeholder': placeholder,
                    'name': name,
                    'id': id_attr,
                    'class': class_attr,
                    'value': value,
                    'displayed': is_displayed,
                    'enabled': is_enabled
                }
                
                input_data.append(element_info)
                
                print(f"  Element {i+1}:")
                print(f"    Tag: {tag_name}")
                print(f"    Type: {element_type}")
                print(f"    Placeholder: {placeholder}")
                print(f"    Name: {name}")
                print(f"    ID: {id_attr}")
                print(f"    Class: {class_attr}")
                print(f"    Value: {value}")
                print(f"    Displayed: {is_displayed}")
                print(f"    Enabled: {is_enabled}")
                print()
                
            except Exception as e:
                print(f"  Element {i+1}: Lỗi đọc - {e}")
        
        # Lưu dữ liệu input vào file JSON
        with open("input_analysis.json", "w", encoding="utf-8") as f:
            json.dump(input_data, f, ensure_ascii=False, indent=2)
        print("💾 Đã lưu phân tích input: input_analysis.json")
        
        # 2. Tìm kiếm cụ thể các loại input
        print("\n2️⃣ TÌM KIẾM CỤ THỂ:")
        
        # Password inputs
        print("\n🔒 TÌM INPUT PASSWORD:")
        password_selectors = [
            'input[type="password"]',
            '*[type="password"]',
            'input[placeholder*="password"]',
            'input[placeholder*="Password"]',
            'input[placeholder*="mật khẩu"]',
            'input[placeholder*="Mật khẩu"]',
            'input[name*="password"]',
            'input[name*="pass"]'
        ]
        
        password_found = False
        for selector in password_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"  ✅ Tìm thấy {len(elements)} element với selector: {selector}")
                    for j, elem in enumerate(elements):
                        print(f"    Element {j+1}: displayed={elem.is_displayed()}, enabled={elem.is_enabled()}")
                    password_found = True
                else:
                    print(f"  ❌ Không tìm thấy với selector: {selector}")
            except Exception as e:
                print(f"  ❌ Lỗi với selector {selector}: {e}")
        
        if not password_found:
            print(f"  {Fore.RED}⚠️  KHÔNG TÌM THẤY INPUT PASSWORD NÀO!{Style.RESET_ALL}")
        
        # Text inputs
        print("\n📝 TÌM INPUT TEXT:")
        text_selectors = [
            'input[type="text"]',
            'input:not([type])',
            'input[type=""]'
        ]
        
        for selector in text_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"  ✅ Tìm thấy {len(elements)} element với selector: {selector}")
                else:
                    print(f"  ❌ Không tìm thấy với selector: {selector}")
            except Exception as e:
                print(f"  ❌ Lỗi với selector {selector}: {e}")
        
        # 3. Kiểm tra JavaScript và dynamic content
        print("\n3️⃣ KIỂM TRA DYNAMIC CONTENT:")
        
        # Chờ thêm để các element dynamic load
        print("⏳ Chờ dynamic content load...")
        time.sleep(5)
        
        # Kiểm tra lại sau khi chờ
        password_elements_after = driver.find_elements(By.CSS_SELECTOR, 'input[type="password"]')
        print(f"🔒 Sau khi chờ: Tìm thấy {len(password_elements_after)} input password")
        
        # 4. Thử tương tác với trang
        print("\n4️⃣ THỬ TƯƠNG TÁC:")
        
        # Click vào trang để trigger events
        try:
            body = driver.find_element(By.CSS_SELECTOR, 'body')
            body.click()
            print("✅ Đã click vào body")
            time.sleep(2)
        except:
            print("❌ Không thể click vào body")
        
        # Scroll trang
        try:
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            print("✅ Đã scroll trang")
            time.sleep(2)
        except:
            print("❌ Không thể scroll")
        
        # Kiểm tra lại sau tương tác
        password_elements_final = driver.find_elements(By.CSS_SELECTOR, 'input[type="password"]')
        print(f"🔒 Sau tương tác: Tìm thấy {len(password_elements_final)} input password")
        
        # 5. Lưu HTML source
        print("\n5️⃣ LƯU DỮ LIỆU DEBUG:")
        
        try:
            with open("page_source_analysis.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print("📄 Đã lưu HTML source: page_source_analysis.html")
        except:
            print("❌ Không thể lưu HTML source")
        
        # Chụp screenshot cuối
        driver.save_screenshot("final_analysis.png")
        print("📸 Đã chụp screenshot cuối: final_analysis.png")
        
        # 6. Đề xuất giải pháp
        print(f"\n6️⃣ ĐỀ XUẤT GIẢI PHÁP:")
        print("="*30)
        
        if not password_found:
            print(f"{Fore.YELLOW}💡 NGUYÊN NHÂN CÓ THỂ:{Style.RESET_ALL}")
            print("1. Form được load bằng JavaScript (cần chờ lâu hơn)")
            print("2. Input password được tạo động sau khi tương tác")
            print("3. Website sử dụng iframe hoặc shadow DOM")
            print("4. Input password có thuộc tính đặc biệt")
            print("5. Website có anti-bot detection")
            
            print(f"\n{Fore.GREEN}🔧 GIẢI PHÁP ĐỀ XUẤT:{Style.RESET_ALL}")
            print("1. Tăng thời gian chờ (sleep 15-20s)")
            print("2. Thêm tương tác trước khi tìm element")
            print("3. Sử dụng WebDriverWait với điều kiện cụ thể")
            print("4. Kiểm tra iframe")
            print("5. Thử headless=False để xem giao diện thực")
        
        # 7. Test manual
        print(f"\n7️⃣ TEST MANUAL:")
        manual_test = input(f"\n{Fore.CYAN}Có muốn test thủ công không? (y/n): {Style.RESET_ALL}").lower()
        
        if manual_test == 'y':
            print("🖱️  Browser sẽ ở lại mở, bạn có thể:")
            print("1. Kiểm tra form bằng mắt")
            print("2. Thử điền thủ công")
            print("3. Mở Developer Tools (F12)")
            print("4. Kiểm tra Console errors")
            
            input(f"\n{Fore.CYAN}Nhấn Enter khi đã kiểm tra xong...{Style.RESET_ALL}")
        
        print(f"\n{Fore.GREEN}✅ PHÂN TÍCH HOÀN THÀNH!{Style.RESET_ALL}")
        print("📁 Kiểm tra các file:")
        print("  - full_page_analysis.png")
        print("  - final_analysis.png") 
        print("  - input_analysis.json")
        print("  - page_source_analysis.html")
        
    except Exception as e:
        print(f"❌ Lỗi phân tích: {e}")
        
    finally:
        if driver:
            try:
                input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                driver.quit()
            except:
                pass

def main():
    """Main function"""
    try:
        advanced_form_analysis()
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
