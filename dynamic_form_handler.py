"""
Tool <PERSON><PERSON> lý form động - <PERSON><PERSON><PERSON><PERSON> pháp cho trường hợp không tìm thấy input password
"""

import time
import random
import string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class DynamicFormHandler:
    def __init__(self):
        self.driver = None
        self.chromedriver_path = "drivers/chromedriver.exe"
        self.register_url = "https://www.13win16.com/home/<USER>"
        
    def setup_driver(self, headless=False):
        """Khởi tạo Chrome driver vớ<PERSON> t<PERSON><PERSON> chọn headless"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            if headless:
                chrome_options.add_argument('--headless')
            
            service = Service(self.chromedriver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(1)  # Giảm implicit wait
            
            print("✅ Chrome driver đã khởi tạo")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khởi tạo driver: {e}")
            return False
    
    def wait_for_page_load(self, timeout=20):
        """Chờ trang load hoàn toàn"""
        try:
            # Chờ document ready
            WebDriverWait(self.driver, timeout).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # Chờ thêm cho JavaScript
            time.sleep(5)
            
            print("✅ Trang đã load hoàn toàn")
            return True
        except Exception as e:
            print(f"⚠️  Timeout chờ trang load: {e}")
            return False
    
    def trigger_form_interactions(self):
        """Trigger các tương tác để form hiển thị đầy đủ"""
        try:
            print("🖱️  Trigger tương tác với form...")
            
            # 1. Click vào body
            try:
                body = self.driver.find_element(By.CSS_SELECTOR, 'body')
                body.click()
                time.sleep(1)
            except:
                pass
            
            # 2. Scroll trang
            try:
                self.driver.execute_script("window.scrollTo(0, 300);")
                time.sleep(1)
                self.driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(1)
            except:
                pass
            
            # 3. Tab qua các element
            try:
                ActionChains(self.driver).send_keys(Keys.TAB).perform()
                time.sleep(0.5)
                ActionChains(self.driver).send_keys(Keys.TAB).perform()
                time.sleep(0.5)
            except:
                pass
            
            # 4. Click vào các element có thể trigger form
            potential_triggers = [
                'input',
                'button',
                'form',
                '.form',
                '#register-form',
                '.register'
            ]
            
            for selector in potential_triggers:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        elements[0].click()
                        time.sleep(0.5)
                        break
                except:
                    continue
            
            print("✅ Đã trigger tương tác")
            return True
            
        except Exception as e:
            print(f"⚠️  Lỗi trigger tương tác: {e}")
            return False
    
    def find_inputs_advanced(self):
        """Tìm input với nhiều phương pháp nâng cao"""
        inputs_found = {
            'username': None,
            'password': None,
            'confirm_password': None,
            'fullname': None
        }
        
        print("🔍 Tìm input với phương pháp nâng cao...")
        
        # 1. Chờ và tìm tất cả input
        time.sleep(3)
        all_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input, *[type]')
        
        print(f"📊 Tìm thấy {len(all_inputs)} input elements")
        
        # 2. Phân loại input theo thuộc tính
        text_inputs = []
        password_inputs = []
        
        for inp in all_inputs:
            try:
                if not inp.is_displayed():
                    continue
                    
                input_type = inp.get_attribute('type') or 'text'
                placeholder = (inp.get_attribute('placeholder') or '').lower()
                name = (inp.get_attribute('name') or '').lower()
                id_attr = (inp.get_attribute('id') or '').lower()
                
                if input_type == 'password':
                    password_inputs.append(inp)
                elif input_type in ['text', '', None]:
                    text_inputs.append(inp)
                    
            except:
                continue
        
        print(f"📝 Text inputs: {len(text_inputs)}")
        print(f"🔒 Password inputs: {len(password_inputs)}")
        
        # 3. Gán input theo thứ tự và logic
        if text_inputs:
            inputs_found['username'] = text_inputs[0]
            if len(text_inputs) >= 2:
                inputs_found['fullname'] = text_inputs[1]
        
        if password_inputs:
            inputs_found['password'] = password_inputs[0]
            if len(password_inputs) >= 2:
                inputs_found['confirm_password'] = password_inputs[1]
        
        # 4. Nếu không tìm thấy password, thử các cách khác
        if not password_inputs:
            print("⚠️  Không tìm thấy input password, thử các cách khác...")
            
            # Thử tìm input có thể là password nhưng type bị ẩn
            potential_password = []
            for inp in all_inputs:
                try:
                    if not inp.is_displayed():
                        continue
                        
                    placeholder = (inp.get_attribute('placeholder') or '').lower()
                    name = (inp.get_attribute('name') or '').lower()
                    id_attr = (inp.get_attribute('id') or '').lower()
                    
                    if any(keyword in placeholder + name + id_attr for keyword in ['pass', 'pwd', 'mật', 'khẩu']):
                        potential_password.append(inp)
                        
                except:
                    continue
            
            if potential_password:
                inputs_found['password'] = potential_password[0]
                if len(potential_password) >= 2:
                    inputs_found['confirm_password'] = potential_password[1]
                print(f"✅ Tìm thấy {len(potential_password)} potential password input")
        
        return inputs_found
    
    def fill_form_with_retry(self, username, password, fullname):
        """Điền form với retry và nhiều phương pháp"""
        try:
            print(f"\n🚀 ĐIỀN FORM: {username}")
            print("="*50)
            
            # Điều hướng đến trang
            self.driver.get(self.register_url)
            
            # Chờ trang load
            if not self.wait_for_page_load():
                print("⚠️  Trang load chậm, tiếp tục...")
            
            # Chụp screenshot ban đầu
            self.driver.save_screenshot(f"initial_{username}.png")
            
            # Trigger tương tác
            self.trigger_form_interactions()
            
            # Chờ thêm sau tương tác
            time.sleep(5)
            
            # Tìm input
            inputs = self.find_inputs_advanced()
            
            # Chụp screenshot sau khi tìm input
            self.driver.save_screenshot(f"after_find_{username}.png")
            
            # Điền từng field
            success_count = 0
            
            # 1. Username
            if inputs['username']:
                try:
                    inputs['username'].clear()
                    inputs['username'].send_keys(username)
                    print(f"✅ Username: {username}")
                    success_count += 1
                    time.sleep(1)
                except Exception as e:
                    print(f"❌ Lỗi điền username: {e}")
            else:
                print("❌ Không tìm thấy ô username")
            
            # 2. Password
            if inputs['password']:
                try:
                    inputs['password'].clear()
                    inputs['password'].send_keys(password)
                    print(f"✅ Password: {password}")
                    success_count += 1
                    time.sleep(1)
                except Exception as e:
                    print(f"❌ Lỗi điền password: {e}")
            else:
                print("❌ Không tìm thấy ô password")
                
                # Thử phương pháp khác: gửi password vào input đầu tiên
                print("🔄 Thử gửi password bằng cách khác...")
                try:
                    all_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input')
                    if len(all_inputs) >= 2:
                        all_inputs[1].clear()
                        all_inputs[1].send_keys(password)
                        print("✅ Đã thử điền password vào input thứ 2")
                        success_count += 1
                except Exception as e:
                    print(f"❌ Phương pháp thay thế thất bại: {e}")
            
            # 3. Confirm Password
            if inputs['confirm_password']:
                try:
                    inputs['confirm_password'].clear()
                    inputs['confirm_password'].send_keys(password)
                    print("✅ Confirm Password")
                    success_count += 1
                    time.sleep(1)
                except Exception as e:
                    print(f"❌ Lỗi điền confirm password: {e}")
            else:
                print("⚠️  Không tìm thấy ô confirm password")
            
            # 4. Fullname
            if inputs['fullname']:
                try:
                    inputs['fullname'].clear()
                    inputs['fullname'].send_keys(fullname)
                    print(f"✅ Fullname: {fullname}")
                    success_count += 1
                    time.sleep(1)
                except Exception as e:
                    print(f"❌ Lỗi điền fullname: {e}")
            else:
                print("⚠️  Không tìm thấy ô fullname")
            
            # 5. Checkbox
            try:
                checkbox = self.driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Checkbox")
            except:
                print("⚠️  Không tìm thấy checkbox")
            
            # Chụp screenshot sau khi điền
            self.driver.save_screenshot(f"after_fill_{username}.png")
            
            print(f"\n📊 Đã điền thành công: {success_count}/4 field")
            
            if success_count >= 2:  # Ít nhất username và password
                return True
            else:
                print("❌ Không đủ field để submit")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi điền form: {e}")
            return False
    
    def submit_form(self):
        """Submit form với nhiều phương pháp"""
        try:
            print("\n🚀 SUBMIT FORM...")
            
            # Tìm nút submit
            submit_button = None
            
            submit_methods = [
                ('CSS', 'button[type="submit"]'),
                ('CSS', 'input[type="submit"]'),
                ('XPATH', "//button[contains(text(), 'Đăng ký')]"),
                ('XPATH', "//button[contains(text(), 'Submit')]"),
                ('XPATH', "//button[contains(text(), 'Register')]"),
                ('CSS', 'button:last-child'),
                ('CSS', 'form button')
            ]
            
            for method, selector in submit_methods:
                try:
                    if method == 'CSS':
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    else:
                        element = self.driver.find_element(By.XPATH, selector)
                    
                    if element and element.is_displayed():
                        submit_button = element
                        print(f"✅ Tìm thấy submit button: {selector}")
                        break
                except:
                    continue
            
            if submit_button:
                try:
                    # Scroll đến button
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                    time.sleep(1)
                    
                    # Click
                    submit_button.click()
                    print("🚀 Đã click submit")
                    
                    # Chờ kết quả
                    time.sleep(8)
                    
                    return True
                except Exception as e:
                    print(f"❌ Lỗi click submit: {e}")
                    
                    # Thử phương pháp khác: Enter
                    try:
                        ActionChains(self.driver).send_keys(Keys.ENTER).perform()
                        print("🚀 Đã thử Enter")
                        time.sleep(8)
                        return True
                    except:
                        return False
            else:
                print("❌ Không tìm thấy nút submit")
                
                # Thử submit bằng Enter
                try:
                    ActionChains(self.driver).send_keys(Keys.ENTER).perform()
                    print("🚀 Đã thử submit bằng Enter")
                    time.sleep(8)
                    return True
                except:
                    return False
                    
        except Exception as e:
            print(f"❌ Lỗi submit: {e}")
            return False
    
    def check_result(self, username):
        """Kiểm tra kết quả đăng ký"""
        try:
            # Chụp screenshot kết quả
            self.driver.save_screenshot(f"result_{username}.png")
            
            page_source = self.driver.page_source.lower()
            
            if any(keyword in page_source for keyword in ["thành công", "success", "welcome", "chào mừng"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
                return True
            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already", "existed"]):
                print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                return False
            else:
                print(f"{Fore.RED}❓ Không xác định được kết quả{Style.RESET_ALL}")
                print("📸 Kiểm tra screenshot để debug")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi kiểm tra kết quả: {e}")
            return False
    
    def register_with_dynamic_handling(self, username, password, fullname):
        """Đăng ký với xử lý form động"""
        try:
            if self.fill_form_with_retry(username, password, fullname):
                if self.submit_form():
                    return self.check_result(username)
                else:
                    print("❌ Không thể submit form")
                    return False
            else:
                print("❌ Không thể điền form")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi đăng ký: {e}")
            return False
    
    def run_test(self):
        """Chạy test với form động"""
        print("🔧 TOOL XỬ LÝ FORM ĐỘNG")
        print("="*50)
        
        # Nhập thông tin
        username = input("👤 Username: ").strip() or "testuser123"
        password = input("🔒 Password: ").strip() or "testpass123"
        fullname = input("👨 Fullname: ").strip() or "TRAN HOANG AN"
        
        headless_choice = input("🖥️  Chạy headless? (y/n): ").lower() == 'y'
        
        # Khởi tạo driver
        if not self.setup_driver(headless=headless_choice):
            return
        
        try:
            success = self.register_with_dynamic_handling(username, password, fullname)
            
            if success:
                print(f"\n{Fore.GREEN}🎉 THÀNH CÔNG!{Style.RESET_ALL}")
            else:
                print(f"\n{Fore.RED}❌ THẤT BẠI!{Style.RESET_ALL}")
                
        finally:
            if self.driver:
                try:
                    if not headless_choice:
                        input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                    self.driver.quit()
                except:
                    pass

def main():
    """Main function"""
    try:
        handler = DynamicFormHandler()
        handler.run_test()
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
