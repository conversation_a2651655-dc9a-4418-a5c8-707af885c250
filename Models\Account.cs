using System;

namespace AccountManager.Models
{
    public class Account
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string RealName { get; set; } = string.Empty;
        public AccountStatus Status { get; set; } = AccountStatus.NotStarted;
        public DateTime? CreatedDate { get; set; }
        public DateTime? LastUpdated { get; set; }
        public decimal WebReward { get; set; } = 0;
        public decimal AppReward { get; set; } = 0;
        public decimal TotalReward => WebReward + AppReward;
        public string Notes { get; set; } = string.Empty;
        public bool IsSelected { get; set; } = false;

        public Account()
        {
        }

        public Account(string username, string password, string realName)
        {
            Username = username;
            Password = password;
            RealName = realName;
            CreatedDate = DateTime.Now;
            LastUpdated = DateTime.Now;
        }

        public void UpdateStatus(AccountStatus newStatus, string? notes = null)
        {
            Status = newStatus;
            LastUpdated = DateTime.Now;
            if (!string.IsNullOrEmpty(notes))
            {
                Notes = notes;
            }
        }

        public void UpdateRewards(decimal webReward, decimal appReward)
        {
            WebReward = webReward;
            AppReward = appReward;
            LastUpdated = DateTime.Now;
        }

        public string GetStatusText()
        {
            return Status switch
            {
                AccountStatus.NotStarted => "Chưa bắt đầu",
                AccountStatus.Registering => "Đang đăng ký",
                AccountStatus.RegisterSuccess => "Đăng ký thành công",
                AccountStatus.RegisterFailed => "Đăng ký thất bại",
                AccountStatus.WebRewardClaimed => "Đã nhận thưởng web",
                AccountStatus.AppRewardClaimed => "Đã nhận thưởng app",
                AccountStatus.Completed => "Hoàn thành",
                AccountStatus.Error => "Lỗi",
                _ => "Không xác định"
            };
        }

        public Color GetStatusColor()
        {
            return Status switch
            {
                AccountStatus.NotStarted => Color.Gray,
                AccountStatus.Registering => Color.Orange,
                AccountStatus.RegisterSuccess => Color.Blue,
                AccountStatus.RegisterFailed => Color.Red,
                AccountStatus.WebRewardClaimed => Color.Green,
                AccountStatus.AppRewardClaimed => Color.Purple,
                AccountStatus.Completed => Color.DarkGreen,
                AccountStatus.Error => Color.Red,
                _ => Color.Black
            };
        }
    }

    public enum AccountStatus
    {
        NotStarted,      // Chưa bắt đầu
        Registering,     // Đang đăng ký
        RegisterSuccess, // Đăng ký thành công
        RegisterFailed,  // Đăng ký thất bại
        WebRewardClaimed, // Đã nhận thưởng web
        AppRewardClaimed, // Đã nhận thưởng app
        Completed,       // Hoàn thành (đạt 52D)
        Error           // Lỗi
    }
}
