"""
Cấu hình cho tool đăng ký tài khoản tự động
"""

# URL đăng ký
REGISTER_URL = "https://www.13win16.com/home/<USER>"

# C<PERSON>u hình trình duyệt
BROWSER_CONFIG = {
    "headless": <PERSON><PERSON><PERSON>,  # <PERSON> để chạy ẩn, False để hiển thị
    "window_size": (1366, 768),
    "user_agent_rotation": True,
    "disable_images": True,  # Tăng tốc độ load
    "disable_css": False,
    "page_load_timeout": 30,
    "implicit_wait": 10
}

# <PERSON><PERSON><PERSON> hình proxy
PROXY_CONFIG = {
    "use_proxy": True,
    "proxy_sources": [
        "https://www.proxy-list.download/api/v1/get?type=http",
        "https://raw.githubusercontent.com/TheSpeedX/PROXY-List/master/http.txt",
        "https://raw.githubusercontent.com/clarketm/proxy-list/master/proxy-list-raw.txt"
    ],
    "proxy_timeout": 10,
    "max_proxy_test_threads": 50
}

# <PERSON><PERSON><PERSON> hình đăng ký
REGISTRATION_CONFIG = {
    "max_concurrent_browsers": 5,  # Số trình duyệt chạy đồng thời
    "delay_between_actions": (1, 3),  # Delay ngẫu nhiên giữa các hành động (giây)
    "retry_attempts": 3,
    "success_delay": (5, 10),  # Delay sau khi đăng ký thành công
}

# Cấu hình dữ liệu tài khoản
ACCOUNT_CONFIG = {
    "email_domains": [
        "gmail.com", "yahoo.com", "hotmail.com", "outlook.com",
        "protonmail.com", "tempmail.org", "10minutemail.com"
    ],
    "password_length": 12,
    "phone_country_codes": ["+84", "+1", "+44", "+86", "+91"],
    "min_age": 18,
    "max_age": 65
}

# File lưu kết quả
OUTPUT_CONFIG = {
    "success_file": "successful_accounts.txt",
    "failed_file": "failed_accounts.txt",
    "proxy_file": "working_proxies.txt",
    "log_file": "registration.log"
}

# Selectors cho form đăng ký 13win16.com
FORM_SELECTORS = {
    "username": 'input[name="username"], input[placeholder*="用户名"], input[placeholder*="账号"]',
    "email": 'input[name="email"], input[type="email"], input[placeholder*="邮箱"]',
    "password": 'input[name="password"], input[type="password"], input[placeholder*="密码"]',
    "confirm_password": 'input[name="confirmPassword"], input[name="confirm_password"], input[placeholder*="确认密码"]',
    "phone": 'input[name="phone"], input[name="mobile"], input[placeholder*="手机号"]',
    "submit_button": 'button[type="submit"], input[type="submit"], .submit-btn, .register-btn',
    "captcha": 'input[name="captcha"], input[name="code"], input[placeholder*="验证码"]',
    "terms_checkbox": 'input[type="checkbox"], input[name*="agree"]'
}

# Cấu hình delay và retry
DELAY_CONFIG = {
    "page_load_wait": 5,
    "element_wait": 10,
    "after_input": (0.5, 1.5),
    "after_click": (1, 2),
    "after_submit": 5,
    "retry_delay": 3
}
