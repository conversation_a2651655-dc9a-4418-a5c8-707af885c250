"""
Script Python để khởi động Chrome debug mode
"""

import os
import subprocess
import time
import socket

class ChromeDebugStarter:
    def __init__(self):
        self.chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME', '')),
            "chrome.exe"  # Trong PATH
        ]
        self.debug_port = 9222
        self.user_data_dir = os.path.join(os.getenv('TEMP', ''), 'chrome_debug')

    def find_chrome(self):
        """Tìm Chrome executable"""
        print("🔍 Đang tìm Chrome...")

        for path in self.chrome_paths:
            if os.path.exists(path):
                print(f"✅ Tìm thấy Chrome: {path}")
                return path

        # Thử tìm trong PATH
        try:
            result = subprocess.run(['where', 'chrome.exe'],
                                  capture_output=True, text=True, shell=True)
            if result.returncode == 0:
                chrome_path = result.stdout.strip().split('\n')[0]
                print(f"✅ Tìm thấy Chrome trong PATH: {chrome_path}")
                return chrome_path
        except:
            pass

        print("❌ Không tìm thấy Chrome!")
        return None

    def is_port_in_use(self, port):
        """Kiểm tra port có đang được sử dụng không"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('127.0.0.1', port))
                return result == 0
        except:
            return False

    def kill_chrome_processes(self):
        """Đóng tất cả Chrome processes"""
        print("🔄 Đóng Chrome hiện có...")
        try:
            if HAS_PSUTIL:
                # Sử dụng psutil nếu có
                for proc in psutil.process_iter(['pid', 'name']):
                    if proc.info['name'] and 'chrome.exe' in proc.info['name'].lower():
                        try:
                            proc.terminate()
                        except:
                            pass

                # Chờ processes đóng
                time.sleep(2)

                # Force kill nếu cần
                for proc in psutil.process_iter(['pid', 'name']):
                    if proc.info['name'] and 'chrome.exe' in proc.info['name'].lower():
                        try:
                            proc.kill()
                        except:
                            pass
            else:
                # Sử dụng taskkill nếu không có psutil
                if os.name == 'nt':  # Windows
                    subprocess.run(['taskkill', '/f', '/im', 'chrome.exe'],
                                 capture_output=True, shell=True)
                else:
                    subprocess.run(['pkill', '-f', 'chrome'],
                                 capture_output=True)
                time.sleep(2)

        except Exception as e:
            print(f"⚠️  Lỗi khi đóng Chrome: {e}")

    def start_chrome_debug(self, chrome_path):
        """Khởi động Chrome với debug mode"""
        print("🚀 Đang khởi động Chrome với debug mode...")

        # Tạo user data directory
        os.makedirs(self.user_data_dir, exist_ok=True)

        # Chrome arguments
        args = [
            chrome_path,
            f"--remote-debugging-port={self.debug_port}",
            f"--user-data-dir={self.user_data_dir}",
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-extensions",
            "--disable-plugins",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding"
        ]

        try:
            # Khởi động Chrome
            subprocess.Popen(args, shell=False)

            # Chờ Chrome khởi động
            print("⏳ Chờ Chrome khởi động...")
            for i in range(10):  # Chờ tối đa 10 giây
                time.sleep(1)
                if self.is_port_in_use(self.debug_port):
                    print("✅ Chrome debug mode đã khởi động thành công!")
                    return True
                print(f"   Đang chờ... ({i+1}/10)")

            print("⚠️  Chrome có thể chưa sẵn sàng, nhưng đã được khởi động")
            return True

        except Exception as e:
            print(f"❌ Lỗi khi khởi động Chrome: {e}")
            return False

    def show_instructions(self):
        """Hiển thị hướng dẫn"""
        print("\n" + "="*60)
        print("✅ CHROME DEBUG MODE ĐÃ KHỞI ĐỘNG!")
        print("="*60)
        print(f"📋 Thông tin:")
        print(f"   - Debug Port: {self.debug_port}")
        print(f"   - User Data: {self.user_data_dir}")
        print(f"   - Bạn có thể sử dụng Chrome này để duyệt web bình thường")
        print(f"   - Tool sẽ kết nối với Chrome này để auto điền form")
        print()
        print("⚠️  LƯU Ý:")
        print("   - Không đóng cửa sổ Chrome này khi đang chạy tool")
        print("   - Có thể mở nhiều tab trong Chrome này")
        print("   - Tool sẽ tự động tạo tab mới để đăng ký")
        print()
        print("🎯 Bước tiếp theo:")
        print("   1. Mở website 13win16.com trong Chrome này (tùy chọn)")
        print("   2. Chạy tool: python main.py")
        print("   3. Chọn 'Sử dụng trình duyệt hiện có: y'")
        print("="*60)

    def run(self):
        """Chạy script chính"""
        print("🚀 KHỞI ĐỘNG CHROME DEBUG MODE")
        print("="*50)

        # Tìm Chrome
        chrome_path = self.find_chrome()
        if not chrome_path:
            print("\n❌ Không thể tìm thấy Chrome!")
            print("📋 Hướng dẫn khắc phục:")
            print("   1. Cài đặt Google Chrome từ: https://www.google.com/chrome/")
            print("   2. Hoặc chạy tool với chế độ tạo browser mới")
            print("   3. Hoặc thêm Chrome vào PATH")
            input("\nNhấn Enter để thoát...")
            return False

        # Kiểm tra port đã được sử dụng chưa
        if self.is_port_in_use(self.debug_port):
            print(f"⚠️  Port {self.debug_port} đã được sử dụng!")
            choice = input("Đóng Chrome hiện có và khởi động lại? (y/n): ").lower()
            if choice != 'y':
                print("❌ Hủy khởi động")
                return False

        # Đóng Chrome hiện có
        self.kill_chrome_processes()

        # Khởi động Chrome debug
        if self.start_chrome_debug(chrome_path):
            self.show_instructions()
            return True
        else:
            print("❌ Không thể khởi động Chrome debug mode!")
            return False

def main():
    """Main function"""
    try:
        starter = ChromeDebugStarter()
        success = starter.run()

        if success:
            input("\nNhấn Enter để tiếp tục...")
        else:
            input("\nNhấn Enter để thoát...")

    except KeyboardInterrupt:
        print("\n👋 Đã hủy!")
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")
        input("Nhấn Enter để thoát...")

if __name__ == "__main__":
    main()
