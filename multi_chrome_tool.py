"""
TOOL ĐĂNG KÝ NHIỀU CHROME INSTANCES - FIX THÔNG BÁO
Mỗi tài khoản = 1 Chrome instance riêng
"""

import time
import random
import string
import os
import subprocess
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class MultiChromeTool:
    def __init__(self):
        self.chromedriver_path = "drivers/chromedriver.exe"
        self.screenshot_folder = "chụp màn hình"
        self.account_rewards = {}
        self.target_reward = 52
        self.active_drivers = {}  # {username: driver}
        
        # Tạo folders
        for folder in [self.screenshot_folder, "chrome_profiles"]:
            if not os.path.exists(folder):
                os.makedirs(folder)
                print(f"📁 Đã tạo folder: {folder}")
    
    def create_chrome_instance(self, username):
        """Tạo Chrome instance riêng cho tài khoản"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Profile riêng cho mỗi tài khoản
            profile_path = f"chrome_profiles/{username}"
            chrome_options.add_argument(f'--user-data-dir={profile_path}')
            
            service = Service(self.chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            driver.set_page_load_timeout(30)
            driver.implicitly_wait(3)
            
            self.active_drivers[username] = driver
            print(f"✅ Đã tạo Chrome instance cho: {username}")
            return driver
            
        except Exception as e:
            print(f"❌ Lỗi tạo Chrome cho {username}: {e}")
            return None
    
    def save_screenshot(self, filename, driver):
        """Chụp screenshot"""
        try:
            filepath = os.path.join(self.screenshot_folder, filename)
            driver.save_screenshot(filepath)
            print(f"📸 Screenshot: {filename}")
        except Exception as e:
            print(f"⚠️  Lỗi chụp màn hình: {e}")
    
    def close_all_notifications(self, driver, username):
        """Đóng tất cả thông báo popup"""
        try:
            print("🔔 Đóng thông báo...")
            
            # Chờ thông báo xuất hiện
            time.sleep(5)
            self.save_screenshot(f"04_before_close_notifications_{username}.png", driver)
            
            # Thử đóng popup nhiều lần
            for attempt in range(10):
                closed_something = False
                
                # Các cách đóng popup
                close_methods = [
                    # Nút text
                    ("XPATH", "//button[contains(text(), 'OK')]"),
                    ("XPATH", "//button[contains(text(), 'Đóng')]"),
                    ("XPATH", "//button[contains(text(), 'Close')]"),
                    ("XPATH", "//button[contains(text(), 'Xác nhận')]"),
                    ("XPATH", "//span[contains(text(), 'OK')]"),
                    ("XPATH", "//div[contains(text(), 'OK')]"),
                    
                    # Icon đóng
                    ("CSS", ".close"),
                    ("CSS", ".close-btn"),
                    ("CSS", ".modal-close"),
                    ("CSS", ".popup-close"),
                    ("CSS", "[aria-label='Close']"),
                    
                    # X button
                    ("XPATH", "//button[text()='×']"),
                    ("XPATH", "//span[text()='×']"),
                    
                    # Button trong modal
                    ("CSS", ".modal button"),
                    ("CSS", ".popup button"),
                    ("CSS", ".dialog button")
                ]
                
                for method_type, selector in close_methods:
                    try:
                        if method_type == "XPATH":
                            elements = driver.find_elements(By.XPATH, selector)
                        else:
                            elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        
                        for element in elements:
                            if element.is_displayed() and element.is_enabled():
                                driver.execute_script("arguments[0].scrollIntoView(true);", element)
                                time.sleep(0.5)
                                element.click()
                                print(f"✅ Đã đóng popup: {selector}")
                                closed_something = True
                                time.sleep(1)
                                break
                        
                        if closed_something:
                            break
                    except:
                        continue
                
                # Thử ESC
                if not closed_something:
                    try:
                        driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                        print("✅ Đã nhấn ESC")
                        closed_something = True
                        time.sleep(1)
                    except:
                        pass
                
                if not closed_something:
                    break
            
            self.save_screenshot(f"05_after_close_notifications_{username}.png", driver)
            print("✅ Đã xử lý xong thông báo")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi đóng thông báo: {e}")
            return False
    
    def navigate_to_rewards(self, driver, username):
        """Chuyển đến trang nhận thưởng"""
        try:
            print("🎁 Chuyển đến trang thưởng...")
            
            # Thử click menu thưởng
            reward_links = [
                ("XPATH", "//a[contains(text(), 'Khuyến mãi')]"),
                ("XPATH", "//a[contains(text(), 'Thưởng')]"),
                ("CSS", "[href*='promotion']"),
                ("CSS", "[href*='event']"),
                ("CSS", "[href*='task']")
            ]
            
            navigated = False
            for method_type, selector in reward_links:
                try:
                    if method_type == "XPATH":
                        element = driver.find_element(By.XPATH, selector)
                    else:
                        element = driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if element.is_displayed():
                        element.click()
                        print(f"✅ Đã click menu thưởng: {selector}")
                        navigated = True
                        time.sleep(3)
                        break
                except:
                    continue
            
            # Nếu không click được, điều hướng trực tiếp
            if not navigated:
                reward_urls = [
                    "https://www.13win16.com/home/<USER>",
                    "https://www.13win16.com/home/<USER>"
                ]
                
                for url in reward_urls:
                    try:
                        driver.get(url)
                        print(f"✅ Đã điều hướng đến: {url}")
                        time.sleep(3)
                        navigated = True
                        break
                    except:
                        continue
            
            if navigated:
                self.save_screenshot(f"06_reward_page_{username}.png", driver)
                return True
            else:
                print("⚠️  Không thể chuyển đến trang thưởng")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi chuyển trang thưởng: {e}")
            return False
    
    def register_account(self, username, password, fullname):
        """Đăng ký tài khoản với Chrome instance riêng"""
        try:
            print(f"\n🚀 ĐĂNG KÝ: {username}")
            print("="*40)
            
            # Tạo Chrome instance riêng
            driver = self.create_chrome_instance(username)
            if not driver:
                return False
            
            # Đi đến trang đăng ký
            register_url = "https://www.13win16.com/home/<USER>"
            driver.get(register_url)
            time.sleep(8)
            
            self.save_screenshot(f"01_register_page_{username}.png", driver)
            
            # Tìm và điền form
            inputs = driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
            print(f"📝 Tìm thấy {len(inputs)} ô input")
            
            if len(inputs) < 3:
                print("❌ Không đủ ô input")
                return False
            
            # Điền form
            try:
                inputs[0].clear()
                inputs[0].send_keys(username)
                print(f"✅ Username: {username}")
                time.sleep(1)
                
                inputs[1].clear()
                inputs[1].send_keys(password)
                print(f"✅ Password: {password}")
                time.sleep(1)
                
                if len(inputs) >= 3:
                    inputs[2].clear()
                    inputs[2].send_keys(password)
                    print("✅ Confirm Password")
                    time.sleep(1)
                
                if len(inputs) >= 4:
                    inputs[3].clear()
                    inputs[3].send_keys(fullname)
                    print(f"✅ Fullname: {fullname}")
                    time.sleep(1)
                    
            except Exception as e:
                print(f"❌ Lỗi điền form: {e}")
                return False
            
            self.save_screenshot(f"02_filled_form_{username}.png", driver)
            
            # Checkbox
            try:
                checkbox = driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Checkbox")
            except:
                print("⚠️  Không có checkbox")
            
            # Submit
            try:
                buttons = driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    submit_btn = buttons[-1]
                    driver.execute_script("arguments[0].scrollIntoView(true);", submit_btn)
                    time.sleep(1)
                    submit_btn.click()
                    print("🚀 Đã submit")
                    time.sleep(8)
                else:
                    print("❌ Không tìm thấy nút submit")
                    return False
            except Exception as e:
                print(f"❌ Lỗi submit: {e}")
                return False
            
            self.save_screenshot(f"03_register_result_{username}.png", driver)
            
            # Kiểm tra kết quả
            page_source = driver.page_source.lower()
            
            if any(keyword in page_source for keyword in ["thành công", "success", "welcome"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
                
                # Khởi tạo theo dõi thưởng
                self.account_rewards[username] = {
                    'register': 13,
                    'red_envelope': 5,  # Ước tính
                    'contact': 4,
                    'app_login': 0,
                    'total': 22
                }
                
                print(f"💰 {username}: 22/52 D")
                
                # Xử lý thông báo và chuyển trang
                self.close_all_notifications(driver, username)
                self.navigate_to_rewards(driver, username)
                
                # Lưu tài khoản
                with open("accounts.txt", "a", encoding="utf-8") as f:
                    f.write(f"{username}|{password}|{fullname}\n")
                
                return True
                
            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already"]):
                print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                return False
            else:
                print(f"{Fore.RED}❓ Không rõ kết quả{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi đăng ký: {e}")
            return False
    
    def generate_password(self):
        """Tạo mật khẩu ngẫu nhiên"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(8)) + "123"
    
    def run_batch_registration(self):
        """Chạy đăng ký hàng loạt"""
        print("🚀 ĐĂNG KÝ HÀNG LOẠT - NHIỀU CHROME")
        print("="*50)
        
        # Cấu hình
        prefix = input("👤 Prefix username: ").strip() or "taolatrumnohu"
        try:
            count = int(input("📝 Số lượng: "))
            start = int(input("📝 Bắt đầu từ số: ") or "1")
        except:
            print("❌ Số không hợp lệ!")
            return
        
        fullname = "TRAN HOANG AN"
        
        print(f"\n📋 Sẽ đăng ký {count} tài khoản:")
        print(f"👤 {prefix}{start} → {prefix}{start+count-1}")
        print(f"👨 Họ tên: {fullname}")
        print(f"🌐 Mỗi tài khoản = 1 Chrome instance riêng")
        
        if input("Tiếp tục? (y/n): ").lower() != 'y':
            return
        
        success_count = 0
        failed_count = 0
        
        try:
            for i in range(count):
                num = start + i
                username = f"{prefix}{num}"
                password = self.generate_password()
                
                print(f"\n📊 {i+1}/{count}: {username}")
                
                if self.register_account(username, password, fullname):
                    success_count += 1
                    print(f"{Fore.GREEN}✅ Thành công: {success_count}{Style.RESET_ALL}")
                else:
                    failed_count += 1
                    print(f"{Fore.RED}❌ Thất bại: {failed_count}{Style.RESET_ALL}")
                
                # Delay
                if i < count - 1:
                    delay = random.randint(3, 8)
                    print(f"⏳ Chờ {delay}s...")
                    time.sleep(delay)
            
            # Kết quả
            print(f"\n📊 KẾT QUẢ:")
            print(f"✅ Thành công: {success_count}")
            print(f"❌ Thất bại: {failed_count}")
            print(f"🌐 Chrome instances đang chạy: {len(self.active_drivers)}")
            
            # Hiển thị thưởng
            print(f"\n💰 TÌNH TRẠNG THƯỞNG:")
            incomplete_count = 0
            for username, rewards in self.account_rewards.items():
                if rewards['total'] >= self.target_reward:
                    print(f"✅ {username}: {rewards['total']}/52 D")
                else:
                    print(f"⏳ {username}: {rewards['total']}/52 D (thiếu {52-rewards['total']}D)")
                    incomplete_count += 1
            
            if incomplete_count > 0:
                print(f"\n{Fore.YELLOW}📱 HƯỚNG DẪN NHẬN THÊM THƯỞNG:{Style.RESET_ALL}")
                print("1. Mở app 13win trên điện thoại/BlueStacks")
                print("2. Đăng nhập từng tài khoản")
                print("3. Nhận thưởng app (+35D)")
                print(f"4. Khi tất cả tài khoản đạt 52D, tool sẽ cho phép đóng Chrome")
            
        except KeyboardInterrupt:
            print(f"\n⚠️  Đã dừng!")
        
        finally:
            # Kiểm tra mục tiêu 52D
            all_complete = all(r['total'] >= self.target_reward for r in self.account_rewards.values())
            
            if all_complete:
                print(f"\n{Fore.GREEN}🎉 TẤT CẢ TÀI KHOẢN ĐÃ ĐẠT 52D!{Style.RESET_ALL}")
                choice = input(f"{Fore.CYAN}Đóng tất cả Chrome? (y/n): {Style.RESET_ALL}").lower()
                if choice == 'y':
                    self.close_all_chrome()
            else:
                incomplete = [u for u, r in self.account_rewards.items() if r['total'] < self.target_reward]
                print(f"\n{Fore.YELLOW}⚠️  CÒN {len(incomplete)} TÀI KHOẢN CHƯA ĐẠT 52D{Style.RESET_ALL}")
                print("🌐 Chrome instances vẫn mở để hoàn thành thủ công")
                print("📱 Sau khi đăng nhập app và nhận đủ thưởng, chạy lại tool để đóng Chrome")
    
    def close_all_chrome(self):
        """Đóng tất cả Chrome instances"""
        for username, driver in self.active_drivers.items():
            try:
                driver.quit()
                print(f"✅ Đã đóng Chrome cho: {username}")
            except:
                pass
        self.active_drivers.clear()

def main():
    """Main function"""
    try:
        tool = MultiChromeTool()
        tool.run_batch_registration()
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
