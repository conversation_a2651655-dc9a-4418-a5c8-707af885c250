using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Newtonsoft.Json;
using AccountManager.Models;

namespace AccountManager.Services
{
    public class AccountService
    {
        private readonly string _accountsFilePath = "accounts.txt";
        private readonly string _statusFilePath = "account_status.json";
        private List<Account> _accounts = new List<Account>();

        public event EventHandler<AccountEventArgs>? AccountUpdated;

        public List<Account> GetAllAccounts()
        {
            return _accounts.ToList();
        }

        public void LoadAccounts()
        {
            _accounts.Clear();

            // Load từ accounts.txt
            if (File.Exists(_accountsFilePath))
            {
                var lines = File.ReadAllLines(_accountsFilePath);
                foreach (var line in lines)
                {
                    if (string.IsNullOrWhiteSpace(line)) continue;

                    var parts = line.Split('|');
                    if (parts.Length >= 3)
                    {
                        var account = new Account(parts[0].Trim(), parts[1].Trim(), parts[2].Trim());
                        _accounts.Add(account);
                    }
                }
            }

            // Load trạng thái từ JSON
            LoadAccountStatus();
        }

        public void SaveAccountStatus()
        {
            try
            {
                var json = JsonConvert.SerializeObject(_accounts, Formatting.Indented);
                File.WriteAllText(_statusFilePath, json);
            }
            catch (Exception ex)
            {
                throw new Exception($"Lỗi lưu trạng thái: {ex.Message}");
            }
        }

        private void LoadAccountStatus()
        {
            if (!File.Exists(_statusFilePath)) return;

            try
            {
                var json = File.ReadAllText(_statusFilePath);
                var savedAccounts = JsonConvert.DeserializeObject<List<Account>>(json);

                if (savedAccounts != null)
                {
                    foreach (var account in _accounts)
                    {
                        var saved = savedAccounts.FirstOrDefault(a => a.Username == account.Username);
                        if (saved != null)
                        {
                            account.Status = saved.Status;
                            account.CreatedDate = saved.CreatedDate;
                            account.LastUpdated = saved.LastUpdated;
                            account.WebReward = saved.WebReward;
                            account.AppReward = saved.AppReward;
                            account.Notes = saved.Notes;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // Ignore errors when loading status
                Console.WriteLine($"Lỗi load trạng thái: {ex.Message}");
            }
        }

        public void UpdateAccountStatus(string username, AccountStatus status, string? notes = null)
        {
            var account = _accounts.FirstOrDefault(a => a.Username == username);
            if (account != null)
            {
                account.UpdateStatus(status, notes);
                SaveAccountStatus();
                AccountUpdated?.Invoke(this, new AccountEventArgs(account));
            }
        }

        public void UpdateAccountRewards(string username, decimal webReward, decimal appReward)
        {
            var account = _accounts.FirstOrDefault(a => a.Username == username);
            if (account != null)
            {
                account.UpdateRewards(webReward, appReward);

                // Tự động cập nhật trạng thái dựa trên thưởng
                if (account.TotalReward >= 52)
                {
                    account.Status = AccountStatus.Completed;
                }
                else if (account.AppReward > 0)
                {
                    account.Status = AccountStatus.AppRewardClaimed;
                }
                else if (account.WebReward > 0)
                {
                    account.Status = AccountStatus.WebRewardClaimed;
                }

                SaveAccountStatus();
                AccountUpdated?.Invoke(this, new AccountEventArgs(account));
            }
        }

        public void AddAccount(string username, string password, string realName)
        {
            if (_accounts.Any(a => a.Username == username))
            {
                throw new Exception($"Tài khoản {username} đã tồn tại!");
            }

            var account = new Account(username, password, realName);
            _accounts.Add(account);

            // Cập nhật file accounts.txt
            var line = $"{username}|{password}|{realName}";
            File.AppendAllText(_accountsFilePath, line + Environment.NewLine);

            SaveAccountStatus();
            AccountUpdated?.Invoke(this, new AccountEventArgs(account));
        }

        public void DeleteAccount(string username)
        {
            var account = _accounts.FirstOrDefault(a => a.Username == username);
            if (account != null)
            {
                _accounts.Remove(account);

                // Cập nhật file accounts.txt
                var lines = _accounts.Select(a => $"{a.Username}|{a.Password}|{a.RealName}").ToArray();
                File.WriteAllLines(_accountsFilePath, lines);

                SaveAccountStatus();
            }
        }

        public void ResetAllStatus()
        {
            foreach (var account in _accounts)
            {
                account.Status = AccountStatus.NotStarted;
                account.WebReward = 0;
                account.AppReward = 0;
                account.Notes = string.Empty;
                account.LastUpdated = DateTime.Now;
            }
            SaveAccountStatus();
        }

        public AccountStats GetStats()
        {
            return new AccountStats
            {
                Total = _accounts.Count,
                NotStarted = _accounts.Count(a => a.Status == AccountStatus.NotStarted),
                Registering = _accounts.Count(a => a.Status == AccountStatus.Registering),
                RegisterSuccess = _accounts.Count(a => a.Status == AccountStatus.RegisterSuccess),
                RegisterFailed = _accounts.Count(a => a.Status == AccountStatus.RegisterFailed),
                WebRewardClaimed = _accounts.Count(a => a.Status == AccountStatus.WebRewardClaimed),
                AppRewardClaimed = _accounts.Count(a => a.Status == AccountStatus.AppRewardClaimed),
                Completed = _accounts.Count(a => a.Status == AccountStatus.Completed),
                Error = _accounts.Count(a => a.Status == AccountStatus.Error),
                TotalWebReward = _accounts.Sum(a => a.WebReward),
                TotalAppReward = _accounts.Sum(a => a.AppReward),
                TotalReward = _accounts.Sum(a => a.TotalReward)
            };
        }
    }

    public class AccountEventArgs : EventArgs
    {
        public Account Account { get; }

        public AccountEventArgs(Account account)
        {
            Account = account;
        }
    }

    public class AccountStats
    {
        public int Total { get; set; }
        public int NotStarted { get; set; }
        public int Registering { get; set; }
        public int RegisterSuccess { get; set; }
        public int RegisterFailed { get; set; }
        public int WebRewardClaimed { get; set; }
        public int AppRewardClaimed { get; set; }
        public int Completed { get; set; }
        public int Error { get; set; }
        public decimal TotalWebReward { get; set; }
        public decimal TotalAppReward { get; set; }
        public decimal TotalReward { get; set; }
    }
}
