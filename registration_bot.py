"""
Bo<PERSON> đăng ký tà<PERSON> khoản tự động cho 13win16.com
"""

from selenium.webdriver.common.by import By
from selenium.common.exceptions import NoSuchElementException
import time
import logging
from browser_manager import BrowserManager
from account_generator import AccountGenerator
from config import REGISTER_URL, OUTPUT_CONFIG

class RegistrationBot:
    def __init__(self, proxy=None):
        self.proxy = proxy
        self.browser = None
        self.account_generator = AccountGenerator()
        self.logger = logging.getLogger(__name__)

    def analyze_form(self):
        """Phân tích form đăng ký để tìm selectors"""
        try:
            self.browser = BrowserManager(self.proxy)
            if not self.browser.create_browser():
                return False

            if not self.browser.navigate_to_url(REGISTER_URL):
                return False

            # Chờ trang load
            time.sleep(5)

            # Tìm các input fields phổ biến
            selectors = {}

            # <PERSON>á<PERSON> selector có thể có
            possible_selectors = {
                'username': [
                    'input[name="username"]',
                    'input[name="user"]',
                    'input[name="account"]',
                    'input[placeholder*="用户"]',
                    'input[placeholder*="账号"]',
                    'input[placeholder*="username"]',
                    'input[id*="username"]',
                    'input[id*="user"]'
                ],
                'password': [
                    'input[name="password"]',
                    'input[name="pwd"]',
                    'input[type="password"]',
                    'input[placeholder*="密码"]',
                    'input[placeholder*="password"]',
                    'input[id*="password"]',
                    'input[id*="pwd"]'
                ],
                'confirm_password': [
                    'input[name="confirmPassword"]',
                    'input[name="confirm_password"]',
                    'input[name="repassword"]',
                    'input[placeholder*="确认密码"]',
                    'input[placeholder*="confirm"]',
                    'input[id*="confirm"]'
                ],
                'email': [
                    'input[name="email"]',
                    'input[type="email"]',
                    'input[placeholder*="邮箱"]',
                    'input[placeholder*="email"]',
                    'input[id*="email"]'
                ],
                'phone': [
                    'input[name="phone"]',
                    'input[name="mobile"]',
                    'input[name="tel"]',
                    'input[placeholder*="手机"]',
                    'input[placeholder*="电话"]',
                    'input[placeholder*="phone"]',
                    'input[id*="phone"]',
                    'input[id*="mobile"]'
                ],
                'captcha': [
                    'input[name="captcha"]',
                    'input[name="code"]',
                    'input[name="verifyCode"]',
                    'input[placeholder*="验证码"]',
                    'input[placeholder*="captcha"]',
                    'input[id*="captcha"]',
                    'input[id*="code"]'
                ],
                'submit_button': [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button:contains("注册")',
                    'button:contains("提交")',
                    'button:contains("Register")',
                    'button:contains("Submit")',
                    '.submit-btn',
                    '.register-btn'
                ],
                'terms_checkbox': [
                    'input[type="checkbox"]',
                    'input[name*="agree"]',
                    'input[name*="terms"]',
                    'input[id*="agree"]',
                    'input[id*="terms"]'
                ]
            }

            # Tìm selectors
            for field, selectors_list in possible_selectors.items():
                for selector in selectors_list:
                    try:
                        element = self.browser.driver.find_element(By.CSS_SELECTOR, selector)
                        if element.is_displayed():
                            selectors[field] = selector
                            self.logger.info(f"Tìm thấy {field}: {selector}")
                            break
                    except NoSuchElementException:
                        continue

            # Lưu page source để phân tích
            page_source = self.browser.get_page_source()
            if page_source:
                with open('page_source.html', 'w', encoding='utf-8') as f:
                    f.write(page_source)

            # Chụp screenshot
            self.browser.take_screenshot('registration_form.png')

            self.logger.info(f"Tìm thấy selectors: {selectors}")
            return selectors

        except Exception as e:
            self.logger.error(f"Lỗi khi phân tích form: {e}")
            return {}
        finally:
            if self.browser:
                self.browser.close_browser()

    def register_account(self, selectors):
        """Đăng ký một tài khoản"""
        try:
            # Tạo thông tin tài khoản
            account = self.account_generator.generate_complete_account()

            # Tạo browser
            self.browser = BrowserManager(self.proxy)
            if not self.browser.create_browser():
                return False, account, "Không thể tạo trình duyệt"

            # Điều hướng đến trang đăng ký
            if not self.browser.navigate_to_url(REGISTER_URL):
                return False, account, "Không thể load trang đăng ký"

            # Chờ trang load
            time.sleep(3)

            # Điền form đăng ký
            success = True
            error_msg = ""

            # Username
            if 'username' in selectors:
                element = self.browser.wait_for_element(selectors['username'])
                if element:
                    if not self.browser.safe_send_keys(element, account['username']):
                        success = False
                        error_msg = "Lỗi khi nhập username"

            # Email
            if 'email' in selectors and success:
                element = self.browser.wait_for_element(selectors['email'])
                if element:
                    if not self.browser.safe_send_keys(element, account['email']):
                        success = False
                        error_msg = "Lỗi khi nhập email"

            # Password
            if 'password' in selectors and success:
                element = self.browser.wait_for_element(selectors['password'])
                if element:
                    if not self.browser.safe_send_keys(element, account['password']):
                        success = False
                        error_msg = "Lỗi khi nhập password"

            # Confirm Password
            if 'confirm_password' in selectors and success:
                element = self.browser.wait_for_element(selectors['confirm_password'])
                if element:
                    if not self.browser.safe_send_keys(element, account['password']):
                        success = False
                        error_msg = "Lỗi khi nhập confirm password"

            # Phone
            if 'phone' in selectors and success:
                element = self.browser.wait_for_element(selectors['phone'])
                if element:
                    if not self.browser.safe_send_keys(element, account['phone']):
                        success = False
                        error_msg = "Lỗi khi nhập phone"

            # Terms checkbox
            if 'terms_checkbox' in selectors and success:
                element = self.browser.wait_for_element(selectors['terms_checkbox'])
                if element and not element.is_selected():
                    if not self.browser.safe_click(element):
                        success = False
                        error_msg = "Lỗi khi click checkbox"

            # Xử lý captcha (nếu có)
            if 'captcha' in selectors and success:
                self.logger.warning("Phát hiện captcha - cần xử lý thủ công")
                # Chờ user nhập captcha
                input("Vui lòng nhập captcha và nhấn Enter để tiếp tục...")

            # Submit form
            if 'submit_button' in selectors and success:
                element = self.browser.wait_for_clickable(selectors['submit_button'])
                if element:
                    # Chụp screenshot trước khi submit
                    self.browser.take_screenshot(f'before_submit_{account["username"]}.png')

                    if self.browser.safe_click(element):
                        # Chờ kết quả
                        time.sleep(5)

                        # Chụp screenshot sau khi submit
                        self.browser.take_screenshot(f'after_submit_{account["username"]}.png')

                        # Kiểm tra kết quả
                        page_source = self.browser.get_page_source()

                        # Kiểm tra các dấu hiệu thành công
                        success_indicators = [
                            "注册成功", "registration successful", "welcome",
                            "账号创建成功", "account created"
                        ]

                        error_indicators = [
                            "用户名已存在", "username exists", "email exists",
                            "注册失败", "registration failed", "error"
                        ]

                        if any(indicator in page_source.lower() for indicator in success_indicators):
                            self.logger.info(f"Đăng ký thành công: {account['username']}")
                            return True, account, "Thành công"
                        elif any(indicator in page_source.lower() for indicator in error_indicators):
                            error_msg = "Đăng ký thất bại - thông tin đã tồn tại"
                        else:
                            error_msg = "Không xác định được kết quả đăng ký"
                    else:
                        error_msg = "Lỗi khi click submit"
                else:
                    error_msg = "Không tìm thấy nút submit"

            return success, account, error_msg

        except Exception as e:
            self.logger.error(f"Lỗi khi đăng ký tài khoản: {e}")
            return False, account if 'account' in locals() else {}, str(e)
        finally:
            if self.browser:
                self.browser.close_browser()

    def save_result(self, success, account, error_msg):
        """Lưu kết quả đăng ký"""
        try:
            if success:
                self.account_generator.save_account(account, OUTPUT_CONFIG['success_file'])
                self.logger.info(f"Đã lưu tài khoản thành công: {account.get('username', 'N/A')}")
            else:
                with open(OUTPUT_CONFIG['failed_file'], 'a', encoding='utf-8') as f:
                    f.write(f"Failed: {account.get('username', 'N/A')} - {error_msg}\n")
                self.logger.error(f"Đăng ký thất bại: {account.get('username', 'N/A')} - {error_msg}")
        except Exception as e:
            self.logger.error(f"Lỗi khi lưu kết quả: {e}")
