"""
Script khởi động sạch - xóa file log cũ và bắt đầu mới
"""

import os
import sys
import subprocess
from datetime import datetime
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def clean_logs():
    """Xóa tất cả file log cũ"""
    print(f"{Fore.YELLOW}🧹 Dọn dẹp file log cũ...{Style.RESET_ALL}")
    
    log_files = [
        "registration.log",
        "output/registration.log", 
        "logs/registration.log"
    ]
    
    cleaned = 0
    for log_file in log_files:
        if os.path.exists(log_file):
            try:
                os.remove(log_file)
                print(f"✅ Đã xóa: {log_file}")
                cleaned += 1
            except Exception as e:
                print(f"⚠️  Không thể xóa {log_file}: {e}")
    
    if cleaned == 0:
        print("ℹ️  Không có file log cũ nào")
    
    return True

def create_clean_log():
    """Tạo file log mới với encoding UTF-8"""
    print(f"{Fore.CYAN}📝 Tạo file log mới...{Style.RESET_ALL}")
    
    try:
        # Tạo thư mục output nếu chưa có
        os.makedirs('output', exist_ok=True)
        
        # Tạo file log chính
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        with open("registration.log", 'w', encoding='utf-8', newline='\n') as f:
            f.write(f"{timestamp} - INFO - Tool khởi động với encoding UTF-8\n")
            f.write(f"{timestamp} - INFO - Sẵn sàng để bắt đầu đăng ký\n")
        
        print("✅ Đã tạo file log mới: registration.log")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi tạo file log: {e}")
        return False

def setup_encoding():
    """Thiết lập encoding cho Windows"""
    if os.name == 'nt':  # Windows
        try:
            # Set console to UTF-8
            os.system('chcp 65001 >nul')
            
            # Reconfigure stdout/stderr if possible
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
                
            print("✅ Đã thiết lập encoding UTF-8")
            return True
        except Exception as e:
            print(f"⚠️  Không thể thiết lập encoding: {e}")
            return False
    return True

def run_tool():
    """Chạy tool chính"""
    print(f"\n{Fore.GREEN}🚀 Khởi động tool chính...{Style.RESET_ALL}")
    
    try:
        # Chạy tool với encoding đã thiết lập
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        
        # Hiển thị output real-time
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())
        
        process.wait()
        print(f"\n{Fore.GREEN}✅ Tool đã hoàn thành!{Style.RESET_ALL}")
        
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}⚠️  Tool bị dừng bởi người dùng{Style.RESET_ALL}")
        try:
            process.terminate()
        except:
            pass
    except Exception as e:
        print(f"\n{Fore.RED}❌ Lỗi khi chạy tool: {e}{Style.RESET_ALL}")

def main():
    """Main function"""
    print("🧹 CLEAN START - KHỞI ĐỘNG SẠCH")
    print("="*50)
    print("Script này sẽ:")
    print("1. Xóa tất cả file log cũ (có thể gây lỗi encoding)")
    print("2. Thiết lập encoding UTF-8")
    print("3. Tạo file log mới")
    print("4. Chạy tool chính")
    
    confirm = input(f"\n{Fore.GREEN}Tiếp tục? (y/n): {Style.RESET_ALL}").lower()
    if confirm != 'y':
        print("❌ Đã hủy!")
        return
    
    print(f"\n{Fore.CYAN}Bắt đầu quá trình clean start...{Style.RESET_ALL}")
    
    # Bước 1: Thiết lập encoding
    setup_encoding()
    
    # Bước 2: Dọn dẹp file log cũ
    clean_logs()
    
    # Bước 3: Tạo file log mới
    if not create_clean_log():
        print(f"{Fore.RED}❌ Không thể tạo file log mới!{Style.RESET_ALL}")
        return
    
    # Bước 4: Chạy tool
    run_tool()
    
    print(f"\n{Fore.GREEN}🎉 CLEAN START HOÀN THÀNH!{Style.RESET_ALL}")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")
