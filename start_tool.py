"""
Script khởi động tool với cấu hình encoding đúng
"""

import os
import sys
import subprocess

def setup_encoding():
    """Thiết lập encoding cho Windows"""
    if os.name == 'nt':  # Windows
        try:
            # Set console to UTF-8
            os.system('chcp 65001 >nul')
            
            # Reconfigure stdout/stderr if possible
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
                
            print("✅ Đã thiết lập encoding UTF-8")
            return True
        except Exception as e:
            print(f"⚠️  Không thể thiết lập encoding: {e}")
            return False
    return True

def main():
    """Khởi động tool chính"""
    print("🚀 AUTO REGISTRATION TOOL - 13WIN16.COM")
    print("=" * 50)
    
    # Thiết lập encoding
    if not setup_encoding():
        print("⚠️  Cảnh báo: <PERSON><PERSON> thể gặp lỗi hiển thị tiếng Việt")
    
    print("\nChọn tùy chọn:")
    print("1. Chạy tool chính")
    print("2. Test tool")
    print("3. Khởi động Chrome debug mode")
    print("4. Cài đặt dependencies")
    print("5. Thoát")
    
    while True:
        try:
            choice = input("\nNhập lựa chọn (1-5): ").strip()
            
            if choice == "1":
                print("\n🚀 Khởi động tool chính...")
                subprocess.run([sys.executable, "main.py"])
                break
                
            elif choice == "2":
                print("\n🧪 Chạy test...")
                subprocess.run([sys.executable, "test_tool.py"])
                break
                
            elif choice == "3":
                print("\n🌐 Khởi động Chrome debug mode...")
                if os.name == 'nt':
                    subprocess.run(["start_chrome_debug.bat"], shell=True)
                else:
                    print("❌ Chỉ hỗ trợ Windows")
                break
                
            elif choice == "4":
                print("\n📦 Cài đặt dependencies...")
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
                print("✅ Hoàn thành!")
                break
                
            elif choice == "5":
                print("👋 Tạm biệt!")
                break
                
            else:
                print("❌ Lựa chọn không hợp lệ! Vui lòng chọn 1-5")
                
        except KeyboardInterrupt:
            print("\n👋 Tạm biệt!")
            break
        except Exception as e:
            print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
