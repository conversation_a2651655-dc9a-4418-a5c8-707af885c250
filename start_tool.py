"""
Script khởi động tool với cấu hình encoding đúng
"""

import os
import sys
import subprocess

def setup_encoding():
    """Thiết lập encoding cho Windows"""
    if os.name == 'nt':  # Windows
        try:
            # Set console to UTF-8
            os.system('chcp 65001 >nul')

            # Reconfigure stdout/stderr if possible
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')

            print("✅ Đã thiết lập encoding UTF-8")
            return True
        except Exception as e:
            print(f"⚠️  Không thể thiết lập encoding: {e}")
            return False
    else:
        return True

def main():
    """Khởi động tool chính"""
    print("🚀 AUTO REGISTRATION TOOL - 13WIN16.COM")
    print("=" * 50)

    # Thiết lập encoding
    if not setup_encoding():
        print("⚠️  Cảnh báo: <PERSON><PERSON> thể gặp lỗi hiển thị tiếng Việt")

    print("\nChọn tùy chọn:")
    print("1. Chạy tool chính")
    print("2. Clean Start (khởi động sạch)")
    print("3. Test tool")
    print("4. Khởi động Chrome debug mode")
    print("5. Debug tool (kiểm tra lỗi)")
    print("6. Sửa lỗi ChromeDriver")
    print("7. Xem log real-time")
    print("8. Sửa lỗi encoding log")
    print("9. Cài đặt dependencies")
    print("10. Thoát")

    while True:
        try:
            choice = input("\nNhập lựa chọn (1-10): ").strip()

            if choice == "1":
                print("\n🚀 Khởi động tool chính...")
                subprocess.run([sys.executable, "main.py"])
                break

            elif choice == "2":
                print("\n🧹 Clean Start - Khởi động sạch...")
                subprocess.run([sys.executable, "clean_start.py"])
                break

            elif choice == "3":
                print("\n🧪 Chạy test...")
                subprocess.run([sys.executable, "test_tool.py"])
                break

            elif choice == "4":
                print("\n🌐 Khởi động Chrome debug mode...")
                try:
                    subprocess.run([sys.executable, "start_chrome_debug.py"])
                except Exception as e:
                    print(f"❌ Lỗi khi khởi động Chrome: {e}")
                    print("Thử sử dụng batch file...")
                    if os.name == 'nt':
                        subprocess.run(["start_chrome_debug.bat"], shell=True)
                break

            elif choice == "5":
                print("\n🔍 Debug tool...")
                subprocess.run([sys.executable, "debug_tool.py"])
                break

            elif choice == "6":
                print("\n🔧 Sửa lỗi ChromeDriver...")
                print("Chọn phương pháp:")
                print("  a. Tải ChromeDriver portable (Khuyến nghị)")
                print("  b. Xóa cache và tải lại")
                sub_choice = input("Chọn (a/b): ").lower()
                if sub_choice == 'a':
                    subprocess.run([sys.executable, "download_chromedriver.py"])
                else:
                    subprocess.run([sys.executable, "fix_chromedriver_simple.py"])
                break

            elif choice == "7":
                print("\n📋 Xem log real-time...")
                subprocess.run([sys.executable, "show_log.py"])
                break

            elif choice == "8":
                print("\n🔧 Sửa lỗi encoding log...")
                subprocess.run([sys.executable, "fix_log_encoding.py"])
                break

            elif choice == "9":
                print("\n📦 Cài đặt dependencies...")
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
                print("✅ Hoàn thành!")
                break

            elif choice == "10":
                print("👋 Tạm biệt!")
                break

            else:
                print("❌ Lựa chọn không hợp lệ! Vui lòng chọn 1-10")

        except KeyboardInterrupt:
            print("\n👋 Tạm biệt!")
            break
        except Exception as e:
            print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
