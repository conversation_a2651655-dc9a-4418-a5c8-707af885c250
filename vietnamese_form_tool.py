"""
Tool đăng ký đã fix cho form tiếng Việt
Dựa trên kết quả debug: input dùng placeholder tiếng <PERSON>i<PERSON><PERSON> thay vì type="password"
"""

import time
import random
import string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class VietnameseFormTool:
    def __init__(self):
        self.driver = None
        self.chromedriver_path = "drivers/chromedriver.exe"
        self.register_url = "https://www.13win16.com/home/<USER>"
        self.login_url = "https://www.13win16.com/home/<USER>"
        self.task_url = "https://www.13win16.com/home/<USER>"
        
    def setup_driver(self):
        """Khởi tạo Chrome driver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            service = Service(self.chromedriver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(3)
            
            print("✅ Chrome driver đã khởi tạo")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khởi tạo driver: {e}")
            return False
    
    def generate_password(self):
        """Tạo mật khẩu ngẫu nhiên"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(10)) + "123"
    
    def find_vietnamese_inputs(self):
        """Tìm input theo placeholder tiếng Việt"""
        inputs = {
            'username': None,
            'password': None,
            'confirm_password': None,
            'fullname': None
        }
        
        try:
            print("🔍 Tìm input theo placeholder tiếng Việt...")
            
            # Chờ trang load
            time.sleep(5)
            
            # Tìm tất cả input type="text"
            all_text_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
            print(f"📊 Tìm thấy {len(all_text_inputs)} input type='text'")
            
            # Phân loại theo placeholder
            for inp in all_text_inputs:
                try:
                    if not inp.is_displayed():
                        continue
                        
                    placeholder = inp.get_attribute('placeholder') or ''
                    placeholder_lower = placeholder.lower()
                    
                    print(f"📝 Input placeholder: '{placeholder}'")
                    
                    # Username/Phone
                    if any(keyword in placeholder_lower for keyword in ['điện thoại', 'username', 'tài khoản', 'phone']):
                        inputs['username'] = inp
                        print(f"✅ Username input: {placeholder}")
                    
                    # Password
                    elif any(keyword in placeholder_lower for keyword in ['mật khẩu', 'password']):
                        if not inputs['password']:
                            inputs['password'] = inp
                            print(f"✅ Password input: {placeholder}")
                        else:
                            inputs['confirm_password'] = inp
                            print(f"✅ Confirm Password input: {placeholder}")
                    
                    # Fullname
                    elif any(keyword in placeholder_lower for keyword in ['họ', 'tên', 'name', 'fullname']):
                        inputs['fullname'] = inp
                        print(f"✅ Fullname input: {placeholder}")
                        
                except Exception as e:
                    print(f"⚠️  Lỗi đọc input: {e}")
                    continue
            
            # Nếu không tìm thấy theo placeholder, gán theo thứ tự
            if not inputs['username'] and all_text_inputs:
                inputs['username'] = all_text_inputs[0]
                print("📝 Gán username = input đầu tiên")
            
            if not inputs['password'] and len(all_text_inputs) >= 2:
                inputs['password'] = all_text_inputs[1]
                print("📝 Gán password = input thứ 2")
            
            if not inputs['confirm_password'] and len(all_text_inputs) >= 3:
                inputs['confirm_password'] = all_text_inputs[2]
                print("📝 Gán confirm password = input thứ 3")
            
            if not inputs['fullname'] and len(all_text_inputs) >= 4:
                inputs['fullname'] = all_text_inputs[3]
                print("📝 Gán fullname = input thứ 4")
            
            return inputs
            
        except Exception as e:
            print(f"❌ Lỗi tìm input: {e}")
            return inputs
    
    def fill_input_safe(self, element, value, field_name):
        """Điền input an toàn"""
        try:
            if element and element.is_displayed() and element.is_enabled():
                element.clear()
                time.sleep(0.5)
                element.send_keys(value)
                print(f"✅ {field_name}: {value}")
                time.sleep(1)
                return True
            else:
                print(f"❌ {field_name}: Element không khả dụng")
                return False
        except Exception as e:
            print(f"❌ {field_name}: Lỗi điền - {e}")
            return False
    
    def register_account(self, username, password, fullname):
        """Đăng ký tài khoản với form tiếng Việt"""
        try:
            print(f"\n🚀 ĐĂNG KÝ TÀI KHOẢN: {username}")
            print("="*50)
            
            # Điều hướng đến trang đăng ký
            self.driver.get(self.register_url)
            time.sleep(8)  # Chờ lâu hơn cho JavaScript
            
            # Chụp screenshot ban đầu
            self.driver.save_screenshot(f"vn_before_{username}.png")
            
            # Tìm input theo cách mới
            inputs = self.find_vietnamese_inputs()
            
            # Chụp screenshot sau khi tìm
            self.driver.save_screenshot(f"vn_found_{username}.png")
            
            # Điền form
            success_count = 0
            
            # 1. Username
            if self.fill_input_safe(inputs['username'], username, "Username"):
                success_count += 1
            
            # 2. Password
            if self.fill_input_safe(inputs['password'], password, "Password"):
                success_count += 1
            
            # 3. Confirm Password
            if self.fill_input_safe(inputs['confirm_password'], password, "Confirm Password"):
                success_count += 1
            
            # 4. Fullname
            if self.fill_input_safe(inputs['fullname'], fullname, "Fullname"):
                success_count += 1
            
            # 5. Checkbox
            try:
                checkbox = self.driver.find_element(By.CSS_SELECTOR, 'input[type="checkbox"]')
                if not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Checkbox")
            except:
                print("⚠️  Không tìm thấy checkbox")
            
            print(f"\n📊 Đã điền {success_count}/4 field")
            
            if success_count < 2:
                print("❌ Không đủ field để submit")
                return False
            
            # 6. Submit
            print("\n🚀 SUBMIT FORM...")
            
            # Chụp screenshot trước submit
            self.driver.save_screenshot(f"vn_before_submit_{username}.png")
            
            # Tìm nút submit
            submit_button = None
            
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                "//button[contains(text(), 'Đăng ký')]",
                "//button[contains(text(), 'Submit')]"
            ]
            
            for selector in submit_selectors:
                try:
                    if selector.startswith('//'):
                        element = self.driver.find_element(By.XPATH, selector)
                    else:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    
                    if element and element.is_displayed():
                        submit_button = element
                        print(f"✅ Tìm thấy submit: {selector}")
                        break
                except:
                    continue
            
            # Fallback: button cuối cùng
            if not submit_button:
                buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    submit_button = buttons[-1]
                    print("✅ Sử dụng button cuối cùng")
            
            if submit_button:
                try:
                    # Scroll và click
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                    time.sleep(1)
                    submit_button.click()
                    print("🚀 Đã click submit")
                    time.sleep(8)
                except Exception as e:
                    print(f"❌ Lỗi click submit: {e}")
                    return False
            else:
                print("❌ Không tìm thấy nút submit")
                return False
            
            # 7. Kiểm tra kết quả
            self.driver.save_screenshot(f"vn_result_{username}.png")
            
            page_source = self.driver.page_source.lower()
            
            if any(keyword in page_source for keyword in ["thành công", "success", "welcome", "chào mừng"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
                
                # Lưu tài khoản
                with open("successful_accounts.txt", "a", encoding="utf-8") as f:
                    f.write(f"Username: {username}\n")
                    f.write(f"Password: {password}\n")
                    f.write(f"Full Name: {fullname}\n")
                    f.write(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("-" * 50 + "\n")
                
                return True
                
            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already"]):
                print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                return False
                
            else:
                print(f"{Fore.RED}❓ Không xác định được kết quả{Style.RESET_ALL}")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Lỗi đăng ký: {e}{Style.RESET_ALL}")
            return False
    
    def login_and_claim_rewards(self, username, password):
        """Đăng nhập và nhận thưởng"""
        try:
            print(f"\n🔑 ĐĂNG NHẬP VÀ NHẬN THƯỞNG: {username}")
            
            # Đăng nhập
            self.driver.get(self.login_url)
            time.sleep(3)
            
            # Tìm input đăng nhập (cũng dùng type="text")
            text_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="text"]')
            password_inputs = self.driver.find_elements(By.CSS_SELECTOR, 'input[type="password"]')
            
            # Username
            if text_inputs:
                text_inputs[0].clear()
                text_inputs[0].send_keys(username)
                print(f"✅ Login Username: {username}")
            
            # Password (có thể là type="password" ở trang login)
            if password_inputs:
                password_inputs[0].clear()
                password_inputs[0].send_keys(password)
                print(f"✅ Login Password")
            elif len(text_inputs) >= 2:
                text_inputs[1].clear()
                text_inputs[1].send_keys(password)
                print(f"✅ Login Password (text input)")
            
            # Submit login
            login_buttons = self.driver.find_elements(By.CSS_SELECTOR, 'button')
            if login_buttons:
                login_buttons[-1].click()
                print("🚀 Đã submit login")
                time.sleep(5)
            
            # Đi đến trang nhiệm vụ
            self.driver.get(self.task_url)
            time.sleep(5)
            
            # Tìm nút "Nhận"
            claim_buttons = self.driver.find_elements(By.XPATH, "//button[contains(text(), 'Nhận')]")
            
            claimed_count = 0
            for i, btn in enumerate(claim_buttons):
                try:
                    if btn.is_displayed() and btn.is_enabled():
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", btn)
                        time.sleep(1)
                        btn.click()
                        claimed_count += 1
                        print(f"✅ Đã nhận thưởng {i+1}")
                        time.sleep(2)
                except:
                    pass
            
            print(f"🎁 Đã nhận {claimed_count} phần thưởng")
            return claimed_count > 0
            
        except Exception as e:
            print(f"❌ Lỗi đăng nhập/nhận thưởng: {e}")
            return False
    
    def run_single_test(self):
        """Test đăng ký 1 tài khoản"""
        print("🇻🇳 TOOL ĐĂNG KÝ FORM TIẾNG VIỆT")
        print("="*50)
        
        # Nhập thông tin
        username = input("👤 Username: ").strip() or "taolatrumnohu1"
        password = input("🔒 Password: ").strip() or "Test123456"
        fullname = input("👨 Fullname: ").strip() or "TRAN HOANG AN"
        
        # Khởi tạo driver
        if not self.setup_driver():
            return
        
        try:
            # Đăng ký
            if self.register_account(username, password, fullname):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
                
                # Hỏi có muốn nhận thưởng không
                claim_choice = input(f"\n{Fore.YELLOW}Có muốn đăng nhập và nhận thưởng không? (y/n): {Style.RESET_ALL}").lower()
                
                if claim_choice == 'y':
                    if self.login_and_claim_rewards(username, password):
                        print(f"{Fore.GREEN}🎁 ĐÃ NHẬN THƯỞNG!{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.YELLOW}⚠️  Không thể nhận thưởng{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}❌ ĐĂNG KÝ THẤT BẠI!{Style.RESET_ALL}")
                
        finally:
            if self.driver:
                try:
                    input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                    self.driver.quit()
                except:
                    pass

def main():
    """Main function"""
    try:
        tool = VietnameseFormTool()
        tool.run_single_test()
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
