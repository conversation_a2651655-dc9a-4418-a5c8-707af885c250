"""
Chạy tool với hiển thị tiến trình
"""

import subprocess
import threading
import time
import os
import sys
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class ProgressMonitor:
    def __init__(self):
        self.log_file = "registration.log"
        self.running = True

    def monitor_log(self):
        """Theo dõi log file và hiển thị tiến trình"""
        if not os.path.exists(self.log_file):
            return

        # Thử các encoding khác nhau
        encodings = ['utf-8', 'utf-8-sig', 'cp1252', 'latin1']
        file_encoding = 'utf-8'

        for encoding in encodings:
            try:
                with open(self.log_file, 'r', encoding=encoding) as f:
                    f.read()
                file_encoding = encoding
                break
            except UnicodeDecodeError:
                continue

        try:
            with open(self.log_file, 'r', encoding=file_encoding, errors='replace') as f:
                # Đi đến cuối file
                f.seek(0, 2)

                while self.running:
                    line = f.readline()
                    if line:
                        self.process_log_line(line.strip())
                    else:
                        time.sleep(0.1)
        except Exception as e:
            print(f"{Fore.RED}❌ Lỗi khi đọc log: {e}{Style.RESET_ALL}")

    def process_log_line(self, line):
        """Xử lý từng dòng log"""
        if not line:
            return

        # Hiển thị tiến trình proxy
        if "proxy hoạt động" in line.lower() and "[" in line:
            # Extract progress từ [completed/total]
            try:
                start = line.find("[") + 1
                end = line.find("]")
                progress = line[start:end]
                print(f"{Fore.CYAN}🔍 Test proxy: {progress}{Style.RESET_ALL}")
            except:
                pass

        # Hiển thị milestone
        elif "đã test" in line.lower() and "proxy" in line.lower():
            print(f"{Fore.YELLOW}📊 {line.split('INFO - ')[-1]}{Style.RESET_ALL}")

        # Hiển thị kết quả quan trọng
        elif any(keyword in line.lower() for keyword in ["thành công", "thất bại", "hoàn thành", "bắt đầu"]):
            if "thành công" in line.lower():
                print(f"{Fore.GREEN}✅ {line.split('INFO - ')[-1]}{Style.RESET_ALL}")
            elif "thất bại" in line.lower() or "lỗi" in line.lower():
                print(f"{Fore.RED}❌ {line.split('INFO - ')[-1]}{Style.RESET_ALL}")
            else:
                print(f"{Fore.BLUE}ℹ️  {line.split('INFO - ')[-1]}{Style.RESET_ALL}")

    def stop(self):
        """Dừng monitor"""
        self.running = False

def run_tool_with_progress():
    """Chạy tool với monitor tiến trình"""
    print("🚀 AUTO REGISTRATION TOOL - VỚI HIỂN THỊ TIẾN TRÌNH")
    print("="*60)

    # Khởi tạo monitor
    monitor = ProgressMonitor()

    # Bắt đầu monitor thread
    monitor_thread = threading.Thread(target=monitor.monitor_log, daemon=True)
    monitor_thread.start()

    try:
        print(f"{Fore.YELLOW}🔄 Đang khởi động tool...{Style.RESET_ALL}")
        print(f"{Fore.CYAN}💡 Tiến trình sẽ được hiển thị bên dưới:{Style.RESET_ALL}")
        print("-" * 60)

        # Chạy tool chính
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        # Hiển thị output của tool
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                print(output.strip())

        # Chờ process hoàn thành
        process.wait()

        print(f"\n{Fore.GREEN}✅ Tool đã hoàn thành!{Style.RESET_ALL}")

    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}⚠️  Tool bị dừng bởi người dùng{Style.RESET_ALL}")
        try:
            process.terminate()
        except:
            pass
    except Exception as e:
        print(f"\n{Fore.RED}❌ Lỗi: {e}{Style.RESET_ALL}")
    finally:
        monitor.stop()

def main():
    """Main function"""
    try:
        run_tool_with_progress()
    except Exception as e:
        print(f"❌ Lỗi: {e}")

    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
