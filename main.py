"""
Tool tự động đăng ký tài khoản 13win16.com
Sử dụng proxy kh<PERSON><PERSON> nhau cho mỗi trình duyệt
"""

import threading
import time
import sys
import os
from concurrent.futures import ThreadPoolExecutor, as_completed
from colorama import init, Fore, Style
from proxy_manager import ProxyManager
from registration_bot import RegistrationBot
from config import REGISTRATION_CONFIG, OUTPUT_CONFIG
import logging

# Cấu hình encoding cho Windows
if os.name == 'nt':  # Windows
    try:
        os.system('chcp 65001 >nul')  # Set UTF-8 encoding
        sys.stdout.reconfigure(encoding='utf-8')
        sys.stderr.reconfigure(encoding='utf-8')
    except:
        pass

# Khởi tạo colorama
init()

class AutoRegistrationTool:
    def __init__(self):
        self.proxy_manager = ProxyManager()
        self.setup_logging()
        self.successful_registrations = 0
        self.failed_registrations = 0
        self.lock = threading.Lock()

    def setup_logging(self):
        """Cấu hình logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(OUTPUT_CONFIG['log_file'], encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def print_banner(self):
        """In banner tool"""
        banner = f"""
{Fore.CYAN}
╔══════════════════════════════════════════════════════════════╗
║                    AUTO REGISTRATION TOOL                    ║
║                      13win16.com   EDERGHOSTVN               ║
║                                                              ║
║  🚀 Tự động đăng ký tài khoản với proxy khác nhau          ║
║  🌐 Hỗ trợ đa luồng và proxy miễn phí                      ║
║  📊 Theo dõi kết quả real-time                             ║
╚══════════════════════════════════════════════════════════════╝
{Style.RESET_ALL}
        """
        print(banner)

    def get_user_input(self):
        """Lấy input từ user"""
        print(f"{Fore.YELLOW}Cấu hình tool:{Style.RESET_ALL}")

        # Chế độ hoạt động
        print(f"{Fore.CYAN}Chọn chế độ hoạt động:{Style.RESET_ALL}")
        print("1. Tự động tạo thông tin ngẫu nhiên")
        print("2. Nhập thông tin thủ công")

        while True:
            try:
                mode = int(input(f"{Fore.GREEN}Chọn chế độ (1-2): {Style.RESET_ALL}"))
                if mode in [1, 2]:
                    break
                else:
                    print(f"{Fore.RED}Vui lòng chọn 1 hoặc 2{Style.RESET_ALL}")
            except ValueError:
                print(f"{Fore.RED}Vui lòng nhập số hợp lệ{Style.RESET_ALL}")

        manual_info = None
        if mode == 2:
            manual_info = self.get_manual_account_info()

        # Số lượng tài khoản
        while True:
            try:
                if mode == 2:
                    num_accounts = 1  # Chỉ tạo 1 tài khoản khi nhập thủ công
                    print(f"{Fore.YELLOW}Chế độ thủ công: Sẽ tạo 1 tài khoản{Style.RESET_ALL}")
                    break
                else:
                    num_accounts = int(input(f"{Fore.GREEN}Nhập số lượng tài khoản muốn tạo: {Style.RESET_ALL}"))
                    if num_accounts > 0:
                        break
                    else:
                        print(f"{Fore.RED}Vui lòng nhập số lớn hơn 0{Style.RESET_ALL}")
            except ValueError:
                print(f"{Fore.RED}Vui lòng nhập số hợp lệ{Style.RESET_ALL}")

        # Số luồng đồng thời
        max_threads = 1 if mode == 2 else self.get_thread_count()

        # Sử dụng proxy
        use_proxy = input(f"{Fore.GREEN}Sử dụng proxy? (y/n, mặc định: y): {Style.RESET_ALL}").lower()
        use_proxy = use_proxy != 'n'

        # Sử dụng trình duyệt hiện có
        use_existing = input(f"{Fore.GREEN}Sử dụng trình duyệt hiện có? (y/n, mặc định: y): {Style.RESET_ALL}").lower()
        use_existing = use_existing != 'n'

        return num_accounts, max_threads, use_proxy, use_existing, manual_info

    def get_manual_account_info(self):
        """Lấy thông tin tài khoản thủ công"""
        from config import ACCOUNT_CONFIG

        print(f"\n{Fore.YELLOW}Nhập thông tin tài khoản:{Style.RESET_ALL}")
        print(f"{Fore.CYAN}💡 Lưu ý: Họ tên sẽ sử dụng giá trị cố định: {ACCOUNT_CONFIG['fixed_real_name']}{Style.RESET_ALL}")

        username = input(f"{Fore.GREEN}Tên đăng nhập/Số điện thoại: {Style.RESET_ALL}")
        password = input(f"{Fore.GREEN}Mật khẩu: {Style.RESET_ALL}")

        # Cho phép user nhập họ tên hoặc sử dụng mặc định
        custom_name = input(f"{Fore.GREEN}Họ tên thật (Enter để dùng mặc định '{ACCOUNT_CONFIG['fixed_real_name']}'): {Style.RESET_ALL}")
        real_name = custom_name.strip() if custom_name.strip() else ACCOUNT_CONFIG['fixed_real_name']

        return {
            'username': username,
            'password': password,
            'full_name': real_name,
            'phone': username  # Sử dụng username làm phone nếu cần
        }

    def get_thread_count(self):
        """Lấy số luồng từ user"""
        while True:
            try:
                max_threads = int(input(f"{Fore.GREEN}Số trình duyệt chạy đồng thời (1-{REGISTRATION_CONFIG['max_concurrent_browsers']}): {Style.RESET_ALL}"))
                if 1 <= max_threads <= REGISTRATION_CONFIG['max_concurrent_browsers']:
                    return max_threads
                else:
                    print(f"{Fore.RED}Vui lòng nhập số từ 1 đến {REGISTRATION_CONFIG['max_concurrent_browsers']}{Style.RESET_ALL}")
            except ValueError:
                print(f"{Fore.RED}Vui lòng nhập số hợp lệ{Style.RESET_ALL}")

    def prepare_proxies(self, use_proxy):
        """Chuẩn bị proxy"""
        if not use_proxy:
            self.logger.info("Chạy không sử dụng proxy")
            return True

        print(f"{Fore.YELLOW}Đang chuẩn bị proxy...{Style.RESET_ALL}")

        # Thử load proxy đã lưu
        loaded_count = self.proxy_manager.load_working_proxies()

        if loaded_count < 10:  # Nếu có ít hơn 10 proxy, fetch thêm
            print(f"{Fore.YELLOW}Đang tìm kiếm proxy mới...{Style.RESET_ALL}")
            found_count = self.proxy_manager.fetch_and_test_proxies()

            if found_count == 0:
                print(f"{Fore.RED}Không tìm thấy proxy hoạt động nào!{Style.RESET_ALL}")
                use_proxy_anyway = input(f"{Fore.YELLOW}Tiếp tục không sử dụng proxy? (y/n): {Style.RESET_ALL}").lower()
                return use_proxy_anyway == 'y'

        print(f"{Fore.GREEN}Sẵn sàng với {len(self.proxy_manager.working_proxies)} proxy{Style.RESET_ALL}")
        return True

    def analyze_registration_form(self):
        """Phân tích form đăng ký"""
        print(f"{Fore.YELLOW}Đang phân tích form đăng ký...{Style.RESET_ALL}")

        bot = RegistrationBot()
        selectors = bot.analyze_form()

        if not selectors:
            print(f"{Fore.RED}Không thể phân tích form đăng ký!{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}Vui lòng kiểm tra file page_source.html và registration_form.png{Style.RESET_ALL}")
            return None

        print(f"{Fore.GREEN}Đã phân tích form thành công!{Style.RESET_ALL}")
        print(f"{Fore.CYAN}Tìm thấy các trường:{Style.RESET_ALL}")
        for field, selector in selectors.items():
            print(f"  - {field}: {selector}")

        return selectors

    def register_single_account(self, proxy, selectors, account_index, manual_info=None):
        """Đăng ký một tài khoản"""
        try:
            self.logger.info(f"Bắt đầu đăng ký tài khoản #{account_index}")

            bot = RegistrationBot(proxy)
            success, account, error_msg = bot.register_account(selectors, manual_info)

            # Lưu kết quả
            bot.save_result(success, account, error_msg)

            # Cập nhật thống kê
            with self.lock:
                if success:
                    self.successful_registrations += 1
                    print(f"{Fore.GREEN}✓ Tài khoản #{account_index}: {account.get('username', 'N/A')} - THÀNH CÔNG{Style.RESET_ALL}")
                else:
                    self.failed_registrations += 1
                    print(f"{Fore.RED}✗ Tài khoản #{account_index}: THẤT BẠI - {error_msg}{Style.RESET_ALL}")

            # Trả lại proxy để sử dụng lại
            if proxy:
                self.proxy_manager.release_proxy(proxy)

            return success, account, error_msg

        except Exception as e:
            self.logger.error(f"Lỗi khi đăng ký tài khoản #{account_index}: {e}")
            if proxy:
                self.proxy_manager.release_proxy(proxy)
            return False, {}, str(e)

    def run_registration(self, num_accounts, max_threads, use_proxy, selectors, manual_info=None):
        """Chạy đăng ký đa luồng"""
        print(f"{Fore.YELLOW}Bắt đầu đăng ký {num_accounts} tài khoản...{Style.RESET_ALL}")

        with ThreadPoolExecutor(max_workers=max_threads) as executor:
            futures = []

            for i in range(num_accounts):
                # Lấy proxy
                proxy = None
                if use_proxy:
                    proxy = self.proxy_manager.get_proxy()
                    if not proxy:
                        self.logger.warning(f"Không có proxy cho tài khoản #{i+1}")

                # Submit task
                future = executor.submit(self.register_single_account, proxy, selectors, i+1, manual_info)
                futures.append(future)

                # Delay giữa các lần submit
                time.sleep(1)

            # Chờ tất cả hoàn thành
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    self.logger.error(f"Lỗi trong thread: {e}")

    def print_summary(self):
        """In tổng kết"""
        total = self.successful_registrations + self.failed_registrations
        success_rate = (self.successful_registrations / total * 100) if total > 0 else 0

        summary = f"""
{Fore.CYAN}
╔══════════════════════════════════════════════════════════════╗
║                        KẾT QUẢ CUỐI CÙNG                    ║
╠══════════════════════════════════════════════════════════════╣
║  📊 Tổng số tài khoản: {total:>10}                          ║
║  ✅ Thành công: {self.successful_registrations:>15}                          ║
║  ❌ Thất bại: {self.failed_registrations:>17}                          ║
║  📈 Tỷ lệ thành công: {success_rate:>8.1f}%                        ║
╠══════════════════════════════════════════════════════════════╣
║  📁 File kết quả:                                           ║
║     - Thành công: {OUTPUT_CONFIG['success_file']:<20}           ║
║     - Thất bại: {OUTPUT_CONFIG['failed_file']:<22}             ║
║     - Log: {OUTPUT_CONFIG['log_file']:<27}                    ║
╚══════════════════════════════════════════════════════════════╝
{Style.RESET_ALL}
        """
        print(summary)

    def run(self):
        """Chạy tool chính"""
        try:
            self.print_banner()

            # Lấy cấu hình từ user
            num_accounts, max_threads, use_proxy, use_existing, manual_info = self.get_user_input()

            # Cập nhật cấu hình browser
            from config import BROWSER_CONFIG
            BROWSER_CONFIG['use_existing_browser'] = use_existing

            # Chuẩn bị proxy
            if not self.prepare_proxies(use_proxy):
                return

            # Phân tích form đăng ký
            selectors = self.analyze_registration_form()
            if not selectors:
                return

            # Xác nhận trước khi bắt đầu
            print(f"\n{Fore.YELLOW}Chuẩn bị đăng ký:{Style.RESET_ALL}")
            print(f"  - Số tài khoản: {num_accounts}")
            print(f"  - Số luồng: {max_threads}")
            print(f"  - Sử dụng proxy: {'Có' if use_proxy else 'Không'}")
            print(f"  - Trình duyệt hiện có: {'Có' if use_existing else 'Không'}")

            if manual_info:
                print(f"  - Thông tin thủ công: {manual_info['username']} - {manual_info['full_name']}")
            else:
                from config import ACCOUNT_CONFIG
                print(f"  - Username pattern: {ACCOUNT_CONFIG['username_prefix']}1, {ACCOUNT_CONFIG['username_prefix']}2, ...")
                print(f"  - Họ tên cố định: {ACCOUNT_CONFIG['fixed_real_name']}")
                print(f"  - Nếu username đã tồn tại: Tự động tạo username ngẫu nhiên")

            confirm = input(f"\n{Fore.GREEN}Bắt đầu đăng ký? (y/n): {Style.RESET_ALL}").lower()
            if confirm != 'y':
                print(f"{Fore.YELLOW}Đã hủy!{Style.RESET_ALL}")
                return

            # Bắt đầu đăng ký
            start_time = time.time()
            self.run_registration(num_accounts, max_threads, use_proxy, selectors, manual_info)
            end_time = time.time()

            # In tổng kết
            print(f"\n{Fore.GREEN}Hoàn thành trong {end_time - start_time:.1f} giây{Style.RESET_ALL}")
            self.print_summary()

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}Đã dừng bởi người dùng!{Style.RESET_ALL}")
        except Exception as e:
            self.logger.error(f"Lỗi chính: {e}")
            print(f"{Fore.RED}Lỗi: {e}{Style.RESET_ALL}")

if __name__ == "__main__":
    tool = AutoRegistrationTool()
    tool.run()
