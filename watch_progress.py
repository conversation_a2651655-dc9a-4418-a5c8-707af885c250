"""
Script đơn giản để theo dõi tiến trình tool
"""

import os
import time
import subprocess
import sys
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def setup_encoding():
    """Thiết lập encoding"""
    if os.name == 'nt':  # Windows
        try:
            os.system('chcp 65001 >nul')
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass

def watch_log():
    """Theo dõi file log"""
    log_file = "registration.log"
    
    if not os.path.exists(log_file):
        print(f"{Fore.YELLOW}⏳ Chờ file log được tạo...{Style.RESET_ALL}")
        while not os.path.exists(log_file):
            time.sleep(1)
    
    print(f"{Fore.GREEN}📋 <PERSON> dõi tiến trình từ {log_file}{Style.RESET_ALL}")
    print(f"{Fore.YELLOW}Nhấn Ctrl+C để dừng{Style.RESET_ALL}")
    print("="*60)
    
    try:
        with open(log_file, 'r', encoding='utf-8', errors='replace') as f:
            # Đi đến cuối file
            f.seek(0, 2)
            
            while True:
                line = f.readline()
                if line:
                    # Lọc và hiển thị thông tin quan trọng
                    line = line.strip()
                    
                    if any(keyword in line.lower() for keyword in [
                        "bắt đầu", "hoàn thành", "thành công", "thất bại", 
                        "proxy hoạt động", "đã tạo", "đã điều hướng",
                        "phân tích form", "đăng ký tài khoản"
                    ]):
                        # Lấy timestamp và message
                        if " - INFO - " in line:
                            parts = line.split(" - INFO - ")
                            if len(parts) >= 2:
                                timestamp = parts[0].split(",")[0]  # Bỏ milliseconds
                                message = parts[1]
                                
                                # Màu sắc theo nội dung
                                if "thành công" in message.lower():
                                    print(f"{Fore.GREEN}✅ [{timestamp}] {message}{Style.RESET_ALL}")
                                elif "thất bại" in message.lower() or "lỗi" in message.lower():
                                    print(f"{Fore.RED}❌ [{timestamp}] {message}{Style.RESET_ALL}")
                                elif "proxy hoạt động" in message.lower():
                                    print(f"{Fore.CYAN}🌐 [{timestamp}] {message}{Style.RESET_ALL}")
                                elif "bắt đầu" in message.lower():
                                    print(f"{Fore.YELLOW}🚀 [{timestamp}] {message}{Style.RESET_ALL}")
                                else:
                                    print(f"{Fore.WHITE}ℹ️  [{timestamp}] {message}{Style.RESET_ALL}")
                        elif " - ERROR - " in line:
                            parts = line.split(" - ERROR - ")
                            if len(parts) >= 2:
                                timestamp = parts[0].split(",")[0]
                                message = parts[1]
                                print(f"{Fore.RED}❌ [{timestamp}] ERROR: {message}{Style.RESET_ALL}")
                else:
                    time.sleep(0.5)
                    
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}👋 Đã dừng theo dõi{Style.RESET_ALL}")
    except Exception as e:
        print(f"{Fore.RED}❌ Lỗi khi đọc log: {e}{Style.RESET_ALL}")

def run_tool_and_watch():
    """Chạy tool và theo dõi tiến trình"""
    print("🚀 AUTO REGISTRATION TOOL - THEO DÕI TIẾN TRÌNH")
    print("="*60)
    
    # Thiết lập encoding
    setup_encoding()
    
    # Kiểm tra tool có đang chạy không
    try:
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True, shell=True)
        if 'main.py' in result.stdout:
            print(f"{Fore.YELLOW}⚠️  Tool có thể đang chạy rồi{Style.RESET_ALL}")
            choice = input("Theo dõi tool hiện tại? (y/n): ").lower()
            if choice == 'y':
                watch_log()
                return
    except:
        pass
    
    print("Chọn tùy chọn:")
    print("1. Chạy tool mới và theo dõi")
    print("2. Chỉ theo dõi tool hiện tại")
    print("3. Thoát")
    
    while True:
        try:
            choice = input(f"\n{Fore.GREEN}Chọn (1-3): {Style.RESET_ALL}").strip()
            
            if choice == "1":
                print(f"\n{Fore.YELLOW}🚀 Khởi động tool mới...{Style.RESET_ALL}")
                
                # Chạy tool trong background
                env = os.environ.copy()
                env['PYTHONIOENCODING'] = 'utf-8'
                
                subprocess.Popen(
                    [sys.executable, "main.py"],
                    env=env,
                    creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
                )
                
                print(f"{Fore.CYAN}💡 Tool đã được khởi động trong cửa sổ mới{Style.RESET_ALL}")
                time.sleep(2)  # Chờ tool khởi động
                watch_log()
                break
                
            elif choice == "2":
                print(f"\n{Fore.CYAN}👁️  Theo dõi tool hiện tại...{Style.RESET_ALL}")
                watch_log()
                break
                
            elif choice == "3":
                print("👋 Tạm biệt!")
                break
                
            else:
                print("❌ Lựa chọn không hợp lệ!")
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}👋 Tạm biệt!{Style.RESET_ALL}")
            break

def main():
    """Main function"""
    try:
        run_tool_and_watch()
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
