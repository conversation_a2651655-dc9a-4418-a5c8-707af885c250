"""
Script sửa lỗi ChromeDriver
"""

import os
import shutil
import platform
import subprocess
import requests
from pathlib import Path
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

def get_chrome_version():
    """L<PERSON>y phiên bản Chrome đã cài đặt"""
    try:
        # Thử các cách khác nhau để lấy version Chrome
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
        ]
        
        for chrome_path in chrome_paths:
            if os.path.exists(chrome_path):
                result = subprocess.run([chrome_path, "--version"], 
                                      capture_output=True, text=True, shell=True)
                if result.returncode == 0:
                    version = result.stdout.strip().split()[-1]
                    print(f"✅ Tìm thấy Chrome version: {version}")
                    return version
        
        print("⚠️  Không tìm thấy Chrome version, sử dụng version mặc định")
        return "120.0.6099.109"  # Version ổn định
        
    except Exception as e:
        print(f"⚠️  Lỗi khi lấy Chrome version: {e}")
        return "120.0.6099.109"

def clean_chromedriver_cache():
    """Xóa cache ChromeDriver cũ"""
    print(f"{Fore.YELLOW}🧹 Dọn dẹp cache ChromeDriver...{Style.RESET_ALL}")
    
    # Các thư mục cache có thể có
    cache_dirs = [
        os.path.expanduser("~/.wdm"),
        os.path.expanduser("~/AppData/Local/.wdm"),
        os.path.expanduser("~/AppData/Roaming/.wdm"),
        ".wdm"
    ]
    
    cleaned = 0
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                shutil.rmtree(cache_dir)
                print(f"✅ Đã xóa: {cache_dir}")
                cleaned += 1
            except Exception as e:
                print(f"⚠️  Không thể xóa {cache_dir}: {e}")
    
    if cleaned == 0:
        print("ℹ️  Không tìm thấy cache ChromeDriver")
    
    return True

def download_chromedriver_manual():
    """Tải ChromeDriver thủ công"""
    print(f"{Fore.CYAN}📥 Tải ChromeDriver thủ công...{Style.RESET_ALL}")
    
    try:
        # Tạo thư mục drivers
        drivers_dir = "drivers"
        os.makedirs(drivers_dir, exist_ok=True)
        
        # URL ChromeDriver ổn định
        architecture = "win64" if platform.machine().endswith('64') else "win32"
        
        # Sử dụng version ổn định
        version = "120.0.6099.109"
        url = f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_{architecture}.zip"
        
        print(f"📥 Đang tải từ: {url}")
        
        # Tải file
        response = requests.get(url, stream=True)
        if response.status_code == 200:
            zip_path = os.path.join(drivers_dir, "chromedriver.zip")
            with open(zip_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            
            print("✅ Đã tải ChromeDriver")
            
            # Giải nén
            import zipfile
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(drivers_dir)
            
            # Xóa file zip
            os.remove(zip_path)
            
            # Tìm file chromedriver.exe
            chromedriver_path = os.path.join(drivers_dir, "chromedriver.exe")
            if os.path.exists(chromedriver_path):
                print(f"✅ ChromeDriver đã sẵn sàng: {chromedriver_path}")
                return chromedriver_path
            else:
                print("❌ Không tìm thấy chromedriver.exe sau khi giải nén")
                return None
        else:
            print(f"❌ Không thể tải ChromeDriver: HTTP {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Lỗi khi tải ChromeDriver: {e}")
        return None

def test_chromedriver(chromedriver_path=None):
    """Test ChromeDriver"""
    print(f"{Fore.BLUE}🧪 Test ChromeDriver...{Style.RESET_ALL}")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        from selenium.webdriver.chrome.options import Options
        
        # Cấu hình Chrome options
        chrome_options = Options()
        chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        # Tạo service
        if chromedriver_path:
            service = Service(chromedriver_path)
        else:
            from webdriver_manager.chrome import ChromeDriverManager
            service = Service(ChromeDriverManager().install())
        
        # Test tạo driver
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.get("https://www.google.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ ChromeDriver hoạt động tốt! (Test page: {title})")
        return True
        
    except Exception as e:
        print(f"❌ ChromeDriver không hoạt động: {e}")
        return False

def fix_chromedriver():
    """Sửa lỗi ChromeDriver"""
    print("🔧 SỬA LỖI CHROMEDRIVER")
    print("="*50)
    
    # Bước 1: Lấy thông tin hệ thống
    print(f"🖥️  Hệ thống: {platform.system()} {platform.machine()}")
    chrome_version = get_chrome_version()
    
    # Bước 2: Dọn dẹp cache
    clean_chromedriver_cache()
    
    # Bước 3: Test ChromeDriver hiện tại
    print(f"\n{Fore.BLUE}🧪 Test ChromeDriver hiện tại...{Style.RESET_ALL}")
    if test_chromedriver():
        print(f"{Fore.GREEN}✅ ChromeDriver đã hoạt động bình thường!{Style.RESET_ALL}")
        return True
    
    # Bước 4: Tải ChromeDriver thủ công
    print(f"\n{Fore.YELLOW}📥 Tải ChromeDriver mới...{Style.RESET_ALL}")
    chromedriver_path = download_chromedriver_manual()
    
    if chromedriver_path:
        # Test ChromeDriver mới
        if test_chromedriver(chromedriver_path):
            print(f"{Fore.GREEN}✅ ChromeDriver mới hoạt động tốt!{Style.RESET_ALL}")
            return True
    
    print(f"{Fore.RED}❌ Không thể sửa lỗi ChromeDriver{Style.RESET_ALL}")
    return False

def main():
    """Main function"""
    try:
        if fix_chromedriver():
            print(f"\n{Fore.GREEN}🎉 ĐÃ SỬA XONG LỖI CHROMEDRIVER!{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 Bây giờ có thể chạy tool bình thường{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.RED}❌ KHÔNG THỂ SỬA LỖI CHROMEDRIVER{Style.RESET_ALL}")
            print(f"{Fore.YELLOW}💡 Thử các giải pháp khác:{Style.RESET_ALL}")
            print("1. Cài đặt lại Google Chrome")
            print("2. Chạy tool với quyền Administrator")
            print("3. Tắt antivirus tạm thời")
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    input(f"\n{Fore.CYAN}Nhấn Enter để thoát...{Style.RESET_ALL}")

if __name__ == "__main__":
    main()
