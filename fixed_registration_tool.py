"""
TOOL ĐĂNG KÝ + NHẬN THƯỞNG - PHIÊN BẢN FIX LỖI
Khắc phục lỗi invalid selector và cải thiện độ ổn định
"""

import time
import random
import string
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from colorama import init, Fore, Style

# Khởi tạo colorama
init()

class Fixed13WinTool:
    def __init__(self):
        self.driver = None
        self.chromedriver_path = "drivers/chromedriver.exe"
        self.register_url = "https://www.13win16.com/home/<USER>"
        self.login_url = "https://www.13win16.com/home/<USER>"
        self.task_url = "https://www.13win16.com/home/<USER>"
        
    def setup_driver(self):
        """Khởi tạo Chrome driver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1366,768')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            service = Service(self.chromedriver_path)
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.driver.set_page_load_timeout(30)
            self.driver.implicitly_wait(3)
            
            print("✅ Chrome driver đã khởi tạo")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khởi tạo driver: {e}")
            return False
    
    def generate_password(self):
        """Tạo mật khẩu ngẫu nhiên"""
        chars = string.ascii_letters + string.digits
        return ''.join(random.choice(chars) for _ in range(10)) + "123"
    
    def safe_find_element(self, by, value, timeout=10):
        """Tìm element an toàn với timeout"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            return None
    
    def safe_find_elements(self, by, value):
        """Tìm nhiều elements an toàn"""
        try:
            return self.driver.find_elements(by, value)
        except:
            return []
    
    def fill_input_safely(self, element, value, field_name):
        """Điền input an toàn"""
        try:
            if element and element.is_displayed() and element.is_enabled():
                element.clear()
                time.sleep(0.5)
                element.send_keys(value)
                print(f"✅ {field_name}: {value}")
                time.sleep(1)
                return True
            else:
                print(f"❌ {field_name}: Element không khả dụng")
                return False
        except Exception as e:
            print(f"❌ {field_name}: Lỗi điền - {e}")
            return False
    
    def register_account(self, username, password, full_name):
        """Đăng ký tài khoản với xử lý lỗi cải tiến"""
        try:
            print(f"\n🚀 ĐĂNG KÝ TÀI KHOẢN: {username}")
            print("="*50)
            
            # Điều hướng đến trang đăng ký
            self.driver.get(self.register_url)
            time.sleep(5)  # Chờ trang load hoàn toàn
            
            # Chụp screenshot để debug
            try:
                self.driver.save_screenshot(f"before_register_{username}.png")
                print("📸 Đã chụp screenshot trước khi đăng ký")
            except:
                pass
            
            # 1. Tìm và điền Username
            print("\n1️⃣ Tìm ô Username...")
            username_element = None
            
            # Thử nhiều cách tìm username field
            selectors = [
                'input[type="text"]',
                'input[placeholder*="điện thoại"]',
                'input[placeholder*="username"]',
                'input[placeholder*="Username"]',
                'input[name*="username"]',
                'input[name*="phone"]'
            ]
            
            for selector in selectors:
                elements = self.safe_find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    username_element = elements[0]  # Lấy element đầu tiên
                    break
            
            if not username_element:
                # Fallback: lấy input đầu tiên
                all_inputs = self.safe_find_elements(By.CSS_SELECTOR, 'input')
                if all_inputs:
                    username_element = all_inputs[0]
            
            if not self.fill_input_safely(username_element, username, "Username"):
                return False
            
            # 2. Tìm và điền Password
            print("\n2️⃣ Tìm ô Password...")
            password_elements = self.safe_find_elements(By.CSS_SELECTOR, 'input[type="password"]')
            
            if not password_elements:
                print("❌ Không tìm thấy ô password")
                return False
            
            if not self.fill_input_safely(password_elements[0], password, "Password"):
                return False
            
            # 3. Tìm và điền Confirm Password
            print("\n3️⃣ Tìm ô Confirm Password...")
            if len(password_elements) >= 2:
                if not self.fill_input_safely(password_elements[1], password, "Confirm Password"):
                    print("⚠️  Lỗi confirm password, tiếp tục...")
            else:
                print("⚠️  Không tìm thấy ô confirm password")
            
            # 4. Tìm và điền Họ tên
            print("\n4️⃣ Tìm ô Họ tên...")
            name_element = None
            
            # Thử tìm theo placeholder
            name_selectors = [
                'input[placeholder*="Họ"]',
                'input[placeholder*="tên"]',
                'input[placeholder*="Name"]',
                'input[placeholder*="name"]'
            ]
            
            for selector in name_selectors:
                element = self.safe_find_element(By.CSS_SELECTOR, selector, timeout=3)
                if element:
                    name_element = element
                    break
            
            # Nếu không tìm thấy, lấy input text thứ 2
            if not name_element:
                text_inputs = self.safe_find_elements(By.CSS_SELECTOR, 'input[type="text"]')
                if len(text_inputs) >= 2:
                    name_element = text_inputs[1]
            
            if not self.fill_input_safely(name_element, full_name, "Họ tên thật"):
                print("⚠️  Lỗi họ tên, tiếp tục...")
            
            # 5. Xử lý Checkbox
            print("\n5️⃣ Tìm checkbox...")
            try:
                checkbox = self.safe_find_element(By.CSS_SELECTOR, 'input[type="checkbox"]', timeout=3)
                if checkbox and not checkbox.is_selected():
                    checkbox.click()
                    print("✅ Đã tick checkbox")
                elif checkbox:
                    print("✅ Checkbox đã được tick")
                else:
                    print("⚠️  Không tìm thấy checkbox")
            except Exception as e:
                print(f"⚠️  Lỗi checkbox: {e}")
            
            # 6. Tìm và click Submit
            print("\n6️⃣ Tìm nút Submit...")
            submit_button = None
            
            # Thử nhiều cách tìm submit button
            submit_selectors = [
                'button[type="submit"]',
                'input[type="submit"]'
            ]
            
            for selector in submit_selectors:
                element = self.safe_find_element(By.CSS_SELECTOR, selector, timeout=3)
                if element:
                    submit_button = element
                    break
            
            # Thử tìm theo text
            if not submit_button:
                try:
                    submit_button = self.safe_find_element(By.XPATH, "//button[contains(text(), 'Đăng ký') or contains(text(), 'Submit') or contains(text(), 'Register')]", timeout=3)
                except:
                    pass
            
            # Fallback: lấy button cuối cùng
            if not submit_button:
                buttons = self.safe_find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    submit_button = buttons[-1]
            
            if submit_button:
                try:
                    # Scroll đến button
                    self.driver.execute_script("arguments[0].scrollIntoView(true);", submit_button)
                    time.sleep(1)
                    
                    submit_button.click()
                    print("🚀 Đã click submit...")
                    time.sleep(7)  # Chờ lâu hơn để xử lý
                except Exception as e:
                    print(f"❌ Lỗi click submit: {e}")
                    return False
            else:
                print("❌ Không tìm thấy nút submit")
                return False
            
            # 7. Kiểm tra kết quả
            print("\n7️⃣ Kiểm tra kết quả...")
            
            # Chụp screenshot kết quả
            try:
                self.driver.save_screenshot(f"after_register_{username}.png")
                print("📸 Đã chụp screenshot sau đăng ký")
            except:
                pass
            
            page_source = self.driver.page_source.lower()
            
            if any(keyword in page_source for keyword in ["thành công", "success", "welcome", "chào mừng"]):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
                
                # Lưu tài khoản thành công
                with open("successful_accounts.txt", "a", encoding="utf-8") as f:
                    f.write(f"Username: {username}\n")
                    f.write(f"Password: {password}\n")
                    f.write(f"Full Name: {full_name}\n")
                    f.write(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("-" * 50 + "\n")
                
                return True
                
            elif any(keyword in page_source for keyword in ["đã tồn tại", "exists", "already", "existed"]):
                print(f"{Fore.YELLOW}⚠️  Tài khoản đã tồn tại!{Style.RESET_ALL}")
                return False
                
            else:
                print(f"{Fore.RED}❓ Không xác định được kết quả đăng ký{Style.RESET_ALL}")
                print("📄 Kiểm tra screenshot để debug")
                return False
                
        except Exception as e:
            print(f"{Fore.RED}❌ Lỗi đăng ký: {e}{Style.RESET_ALL}")
            return False
    
    def login_account(self, username, password):
        """Đăng nhập tài khoản"""
        try:
            print(f"\n🔑 ĐĂNG NHẬP: {username}")
            
            self.driver.get(self.login_url)
            time.sleep(3)
            
            # Username
            username_field = self.safe_find_element(By.CSS_SELECTOR, 'input[type="text"]', timeout=10)
            if not username_field:
                username_field = self.safe_find_element(By.CSS_SELECTOR, 'input', timeout=5)
            
            if not self.fill_input_safely(username_field, username, "Username"):
                return False
            
            # Password
            password_field = self.safe_find_element(By.CSS_SELECTOR, 'input[type="password"]', timeout=5)
            if not self.fill_input_safely(password_field, password, "Password"):
                return False
            
            # Submit
            login_button = self.safe_find_element(By.CSS_SELECTOR, 'button[type="submit"]', timeout=5)
            if not login_button:
                buttons = self.safe_find_elements(By.CSS_SELECTOR, 'button')
                if buttons:
                    login_button = buttons[-1]
            
            if login_button:
                login_button.click()
                print("🚀 Đã submit đăng nhập...")
                time.sleep(5)
                
                page_source = self.driver.page_source.lower()
                if any(keyword in page_source for keyword in ["dashboard", "profile", "logout", "đăng xuất", "tài khoản"]):
                    print(f"{Fore.GREEN}✅ ĐĂNG NHẬP THÀNH CÔNG!{Style.RESET_ALL}")
                    return True
                else:
                    print(f"{Fore.RED}❌ ĐĂNG NHẬP THẤT BẠI!{Style.RESET_ALL}")
                    return False
            else:
                print("❌ Không tìm thấy nút đăng nhập")
                return False
                
        except Exception as e:
            print(f"❌ Lỗi đăng nhập: {e}")
            return False
    
    def claim_rewards(self):
        """Nhận thưởng từ trang nhiệm vụ"""
        try:
            print(f"\n🎁 NHẬN THƯỞNG TỰ ĐỘNG")
            print("="*40)
            
            self.driver.get(self.task_url)
            time.sleep(5)
            
            print(f"📄 URL: {self.task_url}")
            
            # Chụp screenshot trang nhiệm vụ
            try:
                self.driver.save_screenshot("task_page.png")
                print("📸 Đã chụp screenshot trang nhiệm vụ")
            except:
                pass
            
            # Tìm tất cả nút "Nhận"
            claim_buttons = []
            
            # Thử nhiều cách tìm nút "Nhận"
            selectors = [
                "//button[contains(text(), 'Nhận')]",
                "//div[contains(text(), 'Nhận')]/parent::button",
                "//span[contains(text(), 'Nhận')]/parent::button",
                "//button[contains(@class, 'btn') and contains(text(), 'Nhận')]"
            ]
            
            print("🔍 Tìm các nút 'Nhận'...")
            
            for selector in selectors:
                try:
                    buttons = self.driver.find_elements(By.XPATH, selector)
                    for btn in buttons:
                        if btn not in claim_buttons and btn.is_displayed():
                            claim_buttons.append(btn)
                except Exception as e:
                    print(f"⚠️  Lỗi selector {selector}: {e}")
            
            print(f"📊 Tìm thấy: {len(claim_buttons)} nút có thể nhận")
            
            if not claim_buttons:
                print("⚠️  Không tìm thấy nút 'Nhận' nào")
                return 0
            
            # Click từng nút "Nhận"
            claimed_count = 0
            
            for i, button in enumerate(claim_buttons):
                try:
                    if button.is_enabled() and button.is_displayed():
                        # Scroll đến nút
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", button)
                        time.sleep(1)
                        
                        # Click nút
                        button.click()
                        claimed_count += 1
                        
                        print(f"✅ Đã click nút {i+1}: Nhận thưởng")
                        time.sleep(random.uniform(2, 4))
                        
                    else:
                        print(f"⚠️  Nút {i+1} không thể click")
                        
                except Exception as e:
                    print(f"❌ Lỗi click nút {i+1}: {e}")
            
            print(f"\n📊 Kết quả: Đã nhận {claimed_count} phần thưởng")
            
            # Chụp screenshot sau khi nhận thưởng
            try:
                self.driver.save_screenshot("after_claim_rewards.png")
                print("📸 Đã chụp screenshot sau nhận thưởng")
            except:
                pass
            
            return claimed_count
            
        except Exception as e:
            print(f"❌ Lỗi nhận thưởng: {e}")
            return 0
    
    def run_single_registration(self):
        """Chạy đăng ký 1 tài khoản"""
        print("🎯 TOOL FIX - ĐĂNG KÝ 1 TÀI KHOẢN")
        print("="*50)
        
        # Nhập thông tin
        username = input("👤 Username: ").strip()
        password = input("🔒 Password: ").strip()
        full_name = input("👨 Họ tên thật (mặc định: TRAN HOANG AN): ").strip() or "TRAN HOANG AN"
        
        if not username or not password:
            print("❌ Vui lòng nhập đầy đủ thông tin!")
            return
        
        # Khởi tạo driver
        if not self.setup_driver():
            return
        
        try:
            # Đăng ký
            if self.register_account(username, password, full_name):
                print(f"{Fore.GREEN}🎉 ĐĂNG KÝ THÀNH CÔNG!{Style.RESET_ALL}")
                
                # Đăng nhập và nhận thưởng
                time.sleep(3)
                if self.login_account(username, password):
                    claimed = self.claim_rewards()
                    if claimed > 0:
                        print(f"{Fore.GREEN}🎁 Đã nhận {claimed} phần thưởng!{Style.RESET_ALL}")
                    else:
                        print(f"{Fore.YELLOW}⚠️  Chưa có thưởng hoặc đã nhận hết{Style.RESET_ALL}")
                else:
                    print(f"{Fore.YELLOW}⚠️  Không thể đăng nhập để nhận thưởng{Style.RESET_ALL}")
            else:
                print(f"{Fore.RED}❌ ĐĂNG KÝ THẤT BẠI!{Style.RESET_ALL}")
                
        finally:
            if self.driver:
                try:
                    input(f"\n{Fore.CYAN}Nhấn Enter để đóng browser...{Style.RESET_ALL}")
                    self.driver.quit()
                except:
                    pass

def main():
    """Main function"""
    try:
        tool = Fixed13WinTool()
        tool.run_single_registration()
    except Exception as e:
        print(f"❌ Lỗi: {e}")

if __name__ == "__main__":
    main()
